<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="10"
   height="10"
   viewBox="0 0 2.6458333 2.6458333"
   version="1.1"
   id="svg8"
   inkscape:version="1.0 (4035a4fb49, 2020-05-01)"
   sodipodi:docname="TreeArrow_Expanded_Hovered_Left.svg">
  <defs
     id="defs2" />
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="89.600001"
     inkscape:cx="7.414426"
     inkscape:cy="5.1836668"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     inkscape:document-rotation="0"
     showgrid="true"
     units="px"
     width="10px"
     inkscape:pagecheckerboard="true"
     inkscape:window-width="1624"
     inkscape:window-height="1228"
     inkscape:window-x="1606"
     inkscape:window-y="567"
     inkscape:window-maximized="0">
    <inkscape:grid
       type="xygrid"
       id="grid18"
       originx="0"
       originy="0"
       dotted="false"
       enabled="true"
       snapvisiblegridlinesonly="false" />
    <inkscape:grid
       type="xygrid"
       id="grid148"
       color="#ff3f00"
       opacity="0.1254902"
       originx="0.13229167"
       originy="0.13229167"
       empcolor="#d53f3d"
       empopacity="0.25098039" />
  </sodipodi:namedview>
  <metadata
     id="metadata5">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1">
    <path
       style="opacity:1;fill:none;stroke:#ffffff;stroke-width:0.264583px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="M 0.39687496,2.2489583 H 1.984375 V 0.66145837 Z"
       id="path10" />
  </g>
</svg>
