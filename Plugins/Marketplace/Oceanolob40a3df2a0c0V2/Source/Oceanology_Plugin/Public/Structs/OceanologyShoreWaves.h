#pragma once

#include "Kismet/KismetMathLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "NiagaraComponent.h"
#include "OceanologyShoreWaves.generated.h"

/**
 * This is an auto-generated class from Material Parameters via AOceanologyMaterialToStructConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit the material itself instead!
 * Generated at: 4 April 2025
 **/
USTRUCT(BlueprintType)
struct FOceanologyShoreWaves
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="BaseWaveTransitionFactor")
	float BaseWaveTransitionFactor = 300.0;

	/**  CoastalWaves */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="CoastalWaves")
	bool CoastalWaves = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="DirectionWaveSpeed")
	float DirectionWaveSpeed = 250.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="GlobalNoiseScale")
	float GlobalNoiseScale = 20000.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="GlobalWaveScale")
	float GlobalWaveScale = 0.25;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="SideBreakForce")
	float SideBreakForce = 500.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="WaveHeight")
	float WaveHeight = 1500.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="WaveLength")
	float WaveLength = 4000.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="WavePhaseSpeed")
	float WavePhaseSpeed = 1.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="ShoreWaves", DisplayName="WaveSwayAmplitude")
	float WaveSwayAmplitude = 2000.0;

	FOceanologyShoreWaves()
	{
	}
};

/**
 * This is an auto-generated class from Material Parameters via AOceanologyMaterialToStructConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit the material itself instead!
 * Generated at: 4 April 2025
 **/
UCLASS()
class UOceanologyShoreWavesHelper : public UObject
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category="ShoreWaves")
	static void SetMaterialParameters(UMaterialInstanceDynamic* MID, const FOceanologyShoreWaves& ShoreWaves)
	{
		if (!MID)
		{
			return;
		}

		MID->SetScalarParameterValue("BaseWaveTransitionFactor", ShoreWaves.BaseWaveTransitionFactor);
		MID->SetScalarParameterValue("CoastalWaves", ShoreWaves.CoastalWaves ? 1.0 : 0.0);
		MID->SetScalarParameterValue("DirectionWaveSpeed", ShoreWaves.DirectionWaveSpeed);
		MID->SetScalarParameterValue("GlobalNoiseScale", ShoreWaves.GlobalNoiseScale);
		MID->SetScalarParameterValue("GlobalWaveScale", ShoreWaves.GlobalWaveScale);
		MID->SetScalarParameterValue("SideBreakForce", ShoreWaves.SideBreakForce);
		MID->SetScalarParameterValue("WaveHeight", ShoreWaves.WaveHeight);
		MID->SetScalarParameterValue("WaveLength", ShoreWaves.WaveLength);
		MID->SetScalarParameterValue("WavePhaseSpeed", ShoreWaves.WavePhaseSpeed);
		MID->SetScalarParameterValue("WaveSwayAmplitude", ShoreWaves.WaveSwayAmplitude);
	}

	UFUNCTION(BlueprintPure, Category="ShoreWaves")
	static void LerpShoreWaves(
		const FOceanologyShoreWaves& A, 
		const FOceanologyShoreWaves& B, 
		const float Alpha, 
		FOceanologyShoreWaves& OutResult
	)
	{
		FOceanologyShoreWaves LocalResult;
		LocalResult.BaseWaveTransitionFactor = UKismetMathLibrary::Lerp(A.BaseWaveTransitionFactor, B.BaseWaveTransitionFactor, Alpha);
		LocalResult.CoastalWaves = B.CoastalWaves;
		LocalResult.DirectionWaveSpeed = UKismetMathLibrary::Lerp(A.DirectionWaveSpeed, B.DirectionWaveSpeed, Alpha);
		LocalResult.GlobalNoiseScale = UKismetMathLibrary::Lerp(A.GlobalNoiseScale, B.GlobalNoiseScale, Alpha);
		LocalResult.GlobalWaveScale = UKismetMathLibrary::Lerp(A.GlobalWaveScale, B.GlobalWaveScale, Alpha);
		LocalResult.SideBreakForce = UKismetMathLibrary::Lerp(A.SideBreakForce, B.SideBreakForce, Alpha);
		LocalResult.WaveHeight = UKismetMathLibrary::Lerp(A.WaveHeight, B.WaveHeight, Alpha);
		LocalResult.WaveLength = UKismetMathLibrary::Lerp(A.WaveLength, B.WaveLength, Alpha);
		LocalResult.WavePhaseSpeed = UKismetMathLibrary::Lerp(A.WavePhaseSpeed, B.WavePhaseSpeed, Alpha);
		LocalResult.WaveSwayAmplitude = UKismetMathLibrary::Lerp(A.WaveSwayAmplitude, B.WaveSwayAmplitude, Alpha);
		OutResult = LocalResult;
	}

	UFUNCTION(BlueprintPure, Category="ShoreWaves")
	static void SetNiagaraVariables(
		UNiagaraComponent* Niagara, 
		const FOceanologyShoreWaves& ShoreWaves
	)
	{
		if (!Niagara)
		{
			return;
		}

		Niagara->SetVariableFloat("BaseWaveTransitionFactor", ShoreWaves.BaseWaveTransitionFactor);
		Niagara->SetVariableBool("CoastalWaves", ShoreWaves.CoastalWaves);
		Niagara->SetVariableFloat("DirectionWaveSpeed", ShoreWaves.DirectionWaveSpeed);
		Niagara->SetVariableFloat("GlobalNoiseScale", ShoreWaves.GlobalNoiseScale);
		Niagara->SetVariableFloat("GlobalWaveScale", ShoreWaves.GlobalWaveScale);
		Niagara->SetVariableFloat("SideBreakForce", ShoreWaves.SideBreakForce);
		Niagara->SetVariableFloat("WaveHeight", ShoreWaves.WaveHeight);
		Niagara->SetVariableFloat("WaveLength", ShoreWaves.WaveLength);
		Niagara->SetVariableFloat("WavePhaseSpeed", ShoreWaves.WavePhaseSpeed);
		Niagara->SetVariableFloat("WaveSwayAmplitude", ShoreWaves.WaveSwayAmplitude);
	}
};
