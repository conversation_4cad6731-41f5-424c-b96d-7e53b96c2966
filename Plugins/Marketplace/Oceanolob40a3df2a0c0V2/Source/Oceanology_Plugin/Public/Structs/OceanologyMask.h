#pragma once

#include "Kismet/KismetMathLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "NiagaraComponent.h"
#include "OceanologyMask.generated.h"

/**
 * This is an auto-generated class from Material Parameters via AOceanologyMaterialToStructConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit the material itself instead!
 * Generated at: 4 April 2025
 **/
USTRUCT(BlueprintType)
struct FOceanologyMask
{
	GENERATED_BODY()

	/**  Landscape Masking  */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Mask", meta = (ClampMax = "1.0"), DisplayName="LandscapeVisibilityMask")
	bool LandscapeVisibilityMask = false;

	/** Control Distance To Mask Landscape Edges */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Mask", meta = (ClampMin = "0.05",ClampMax = "50.0"), DisplayName="MaskLandscapeEdges")
	float MaskLandscapeEdges = 0.05;

	/**  Mask Objects */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Mask", meta = (ClampMax = "1.0"), DisplayName="ObjectVisibilityMask")
	bool ObjectVisibilityMask = false;

	FOceanologyMask()
	{
	}
};

/**
 * This is an auto-generated class from Material Parameters via AOceanologyMaterialToStructConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit the material itself instead!
 * Generated at: 4 April 2025
 **/
UCLASS()
class UOceanologyMaskHelper : public UObject
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category="Mask")
	static void SetMaterialParameters(UMaterialInstanceDynamic* MID, const FOceanologyMask& Mask)
	{
		if (!MID)
		{
			return;
		}

		MID->SetScalarParameterValue("LandscapeVisibilityMask", Mask.LandscapeVisibilityMask ? 1.0 : 0.0);
		MID->SetScalarParameterValue("MaskLandscapeEdges", Mask.MaskLandscapeEdges);
		MID->SetScalarParameterValue("ObjectVisibilityMask", Mask.ObjectVisibilityMask ? 1.0 : 0.0);
	}

	UFUNCTION(BlueprintPure, Category="Mask")
	static void LerpMask(
		const FOceanologyMask& A, 
		const FOceanologyMask& B, 
		const float Alpha, 
		FOceanologyMask& OutResult
	)
	{
		FOceanologyMask LocalResult;
		LocalResult.LandscapeVisibilityMask = B.LandscapeVisibilityMask;
		LocalResult.MaskLandscapeEdges = UKismetMathLibrary::Lerp(A.MaskLandscapeEdges, B.MaskLandscapeEdges, Alpha);
		LocalResult.ObjectVisibilityMask = B.ObjectVisibilityMask;
		OutResult = LocalResult;
	}

	UFUNCTION(BlueprintPure, Category="Mask")
	static void SetNiagaraVariables(
		UNiagaraComponent* Niagara, 
		const FOceanologyMask& Mask
	)
	{
		if (!Niagara)
		{
			return;
		}

		Niagara->SetVariableBool("LandscapeVisibilityMask", Mask.LandscapeVisibilityMask);
		Niagara->SetVariableFloat("MaskLandscapeEdges", Mask.MaskLandscapeEdges);
		Niagara->SetVariableBool("ObjectVisibilityMask", Mask.ObjectVisibilityMask);
	}
};
