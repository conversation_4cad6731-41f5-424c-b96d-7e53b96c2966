#pragma once

#include "OceanologyWaveInfo.generated.h"

/**
 * A structure that contains basic Wave Info requested at a specific location on the water surface.
 */
USTRUCT(BlueprintType)
struct FOceanologyWaveInfo
{
	GENERATED_BODY()

	/** This is the final Wave Offset that contains ALL calculations. Contains the WaterBody#ActorLocationZ and the requested Location as well. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	FVector WaveOffset = FVector::ZeroVector;

	/** This is the standard wave offset of the water body, shore waves EXCLUDED.  */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	FVector WaterWaveOffset = FVector::ZeroVector;

	/** Only breaking waves offset. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	FVector BreakingWaveOffset = FVector::ZeroVector;

	/** The depth of the water based on water body actor height and the Ground Data. This will only work with correct ground setup. No clamping. Positive WaterDepth allowed. This happens when you are OUT OF WATER. Use only for specific purposes, otherwise WaterDepthClamped. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	float WaterDepth = 0.0f;

	/** Clamped water depth **/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	float WaterDepthClamped = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	float SDFShoreline = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	float SDFOcean = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Wave Info")
	float SignedFieldDistance = 0.0f;
	
};
