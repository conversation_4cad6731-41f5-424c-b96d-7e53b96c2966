#pragma once

#include "Kismet/KismetMathLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "NiagaraComponent.h"
#include "OceanologyProcedural.generated.h"

/**
 * This is an auto-generated class from Material Parameters via AOceanologyMaterialToStructConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit the material itself instead!
 * Generated at: 29 November 2024
 **/
USTRUCT(BlueprintType)
struct FOceanologyProcedural
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Procedural", DisplayName="NoiseMaskIntensity")
	float NoiseMaskIntensity = 0.1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Procedural", DisplayName="NoiseTiling")
	float NoiseTiling = 64.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Procedural", DisplayName="Turbulance")
	float Turbulance = 0.1;

	FOceanologyProcedural()
	{
	}
};

/**
 * This is an auto-generated class from Material Parameters via AOceanologyMaterialToStructConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit the material itself instead!
 * Generated at: 29 November 2024
 **/
UCLASS()
class UOceanologyProceduralHelper : public UObject
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category="Procedural")
	static void SetMaterialParameters(UMaterialInstanceDynamic* MID, const FOceanologyProcedural& Procedural)
	{
		if (!MID)
		{
			return;
		}

		MID->SetScalarParameterValue("NoiseMaskIntensity", Procedural.NoiseMaskIntensity);
		MID->SetScalarParameterValue("NoiseTiling", Procedural.NoiseTiling);
		MID->SetScalarParameterValue("Turbulance", Procedural.Turbulance);
	}

	UFUNCTION(BlueprintPure, Category="Procedural")
	static void LerpProcedural(
		const FOceanologyProcedural& A, 
		const FOceanologyProcedural& B, 
		const float Alpha, 
		FOceanologyProcedural& OutResult
	)
	{
		FOceanologyProcedural LocalResult;
		LocalResult.NoiseMaskIntensity = UKismetMathLibrary::Lerp(A.NoiseMaskIntensity, B.NoiseMaskIntensity, Alpha);
		LocalResult.NoiseTiling = UKismetMathLibrary::Lerp(A.NoiseTiling, B.NoiseTiling, Alpha);
		LocalResult.Turbulance = UKismetMathLibrary::Lerp(A.Turbulance, B.Turbulance, Alpha);
		OutResult = LocalResult;
	}

	UFUNCTION(BlueprintPure, Category="Procedural")
	static void SetNiagaraVariables(
		UNiagaraComponent* Niagara, 
		const FOceanologyProcedural& Procedural
	)
	{
		if (!Niagara)
		{
			return;
		}

		Niagara->SetVariableFloat("NoiseMaskIntensity", Procedural.NoiseMaskIntensity);
		Niagara->SetVariableFloat("NoiseTiling", Procedural.NoiseTiling);
		Niagara->SetVariableFloat("Turbulance", Procedural.Turbulance);
	}
};
