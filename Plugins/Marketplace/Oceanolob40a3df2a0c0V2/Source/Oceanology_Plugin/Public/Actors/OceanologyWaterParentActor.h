// Copyright 1998-2023 Epic Games, Inc. All Rights Reserved.
// =================================================
// Created by: Galidar
// Project name: Oceanology
// Created on: 2023/12/15
//
// =================================================

#pragma once

#include "CoreMinimal.h"
#include "Enums/OceanologyUpdateWaterMaterialParameters.h"
#include "Enums/OceanologyWaterPresetMode.h"
#include "GameFramework/Actor.h"
#include "Structs/OceanologyActorHeight.h"
#include "Structs/OceanologyCameraOffset.h"
#include "Structs/OceanologyCaustics.h"
#include "Structs/OceanologyComputeNormals.h"
#include "Structs/OceanologyDefaultPresetInclusionGroups.h"
#include "Structs/OceanologyFlipbook.h"
#include "Structs/OceanologyFluidSimulation.h"
#include "Structs/OceanologyFoam.h"
#include "Structs/OceanologyHorizonCorrection.h"
#include "Structs/OceanologyProcedural.h"
#include "Structs/OceanologyRefraction.h"
#include "Structs/OceanologyRVT.h"
#include "Structs/OceanologySurfaceScattering.h"
#include "Structs/OceanologyMask.h"
#include "Structs/OceanologyGGX.h"
#include "Structs/OceanologyGroupedWaterPresets.h"
#include "Structs/OceanologyWaterPresetResult.h"
#include "Structs/OceanologyWaterProjection.h"
#include "Structs/OceanologyWaterQuadTreeSettings.h"
#include "Structs/OceanologyWaveInfo.h"
#include "Structs/OceanologyWetness.h"
#include "OceanologyWaterParentActor.generated.h"

class AOceanologyManager;
class ALandscapeProxy;
class UOceanologyWaterMeshComponent;
class UOceanologyWaveSolverComponent;
class UOceanologyWaterPreset;
class UNiagaraSystem;
class USceneComponent;
class UBoxComponent;
class UPostProcessComponent;
class UNiagaraComponent;
class UStaticMeshComponent;
class UOceanologyUnderwaterComponent;
class AOceanologyInfiniteOcean;
class UDecalComponent;

UCLASS(Abstract)
class OCEANOLOGY_PLUGIN_API AOceanologyWaterParent : public AActor
{
	GENERATED_BODY()

public:
	AOceanologyWaterParent();

public:
	UPROPERTY(BlueprintReadWrite, NonTransactional, meta = (Category = "Default"))
	TObjectPtr<USceneComponent> DefaultSceneRoot;

	UPROPERTY(BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UOceanologyWaterMeshComponent> WaterMeshComponent;

	UPROPERTY(VisibleDefaultsOnly, BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UBoxComponent> UnderwaterBoxVolumeComponent;

	UPROPERTY(VisibleDefaultsOnly, BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UPostProcessComponent> UnderwaterPostProcessComponent;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UOceanologyUnderwaterComponent> UnderwaterComponent;

	UPROPERTY(VisibleDefaultsOnly, BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UStaticMeshComponent> UnderwaterFogMeshComponent;
	
	UPROPERTY(VisibleDefaultsOnly, BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UNiagaraComponent> BubblesComponent;

	UPROPERTY(VisibleDefaultsOnly, BlueprintReadWrite, meta = (Category = "Default"))
	TObjectPtr<UDecalComponent> DLWetnessComponent;

#if WITH_EDITOR

public:
	FSimpleMulticastDelegate OnPropertyUpdated;
#endif

private:
	UPROPERTY(Transient)
	mutable TWeakObjectPtr<ALandscapeProxy> Landscape;

#if WITH_EDITORONLY_DATA
	UPROPERTY()
	FVector LastActorScale = FVector::OneVector;
#endif
	
public:
	UFUNCTION(CallInEditor, Category = "Default", BlueprintCallable)
	void ReloadSettings(const bool Silent = false);

	virtual void Init();
	virtual void InitQuadTreeSettings();
	virtual void RecomputeQuadTreeEffectiveTileSize();

	virtual ALandscapeProxy* FindLandscape() const;

	virtual bool ShouldTickIfViewportsOnly() const override
	{
		return true;
	}

#if WITH_EDITOR
	virtual bool CanChangeIsSpatiallyLoadedFlag() const override { return false; }
#endif

	virtual UNiagaraSystem* GetDefaultBubblesEffect();

	UFUNCTION(BlueprintPure, Category="Water Surface")
	float GetWaterDensity(const AActor* Actor);

	virtual int32 GetDefaultWaterVolumeOverlapPriority();

protected:
	virtual void OnConstruction(const FTransform& Transform) override;
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaSeconds) override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Preset")
	FOceanologyGroupedWaterPresets GroupedWaterPresets;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Preset")
	EOceanologyWaterPresetMode PresetMode = EOceanologyWaterPresetMode::ApplyPresetToCurrentSettings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Preset")
	TArray<FName> PresetInclusionGroups = FOceanologyDefaultPresetInclusionGroups().DefaultPresetInclusionGroups;

	virtual UOceanologyWaterPreset* GetPreset() const;
	virtual UOceanologyWaterPreset* GetDefaultPreset() const;

	void InitPreset();
	void LoadCurrentPreset();

	void SetWaterPresetResult(
		const FOceanologyWaterPresetResult& WaterPresetResult,
		const bool IncludeWaveTransition
	);

#if WITH_EDITOR
	void OnPresetPropertyChanged(
		const EOceanologyWaterPresetMode InPresetMode,
		UOceanologyWaterPreset* InPreset,
		const TArray<FName>& InPresetInclusionGroups
	);

	void OnPresetGroupedPropertyChanged(
		const EOceanologyWaterPresetMode InPresetMode,
		const TArray<FName>& InPresetInclusionGroups
	);

public:
	virtual void RefreshPreset();
#endif

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Waves")
	TSubclassOf<UOceanologyWaveSolverComponent> WaveSolverClass;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = "Waves")
	UOceanologyWaveSolverComponent* WaveSolver = nullptr;

protected:
	bool WaveSolverSetup = false;

public:
	UFUNCTION(Category= "Waves")
	bool SetupWaveSolvers();

	UFUNCTION(BlueprintCallable, Category="Waves")
	UOceanologyWaveSolverComponent* GetWaveSolver() const { return WaveSolver; }

	UFUNCTION(
		BlueprintCallable, BlueprintPure = false,
		Category="Waves",
		meta = (
			DeterminesOutputType = "InWaveSolverClass",
			DynamicOutputParam = "OutWaveSolver",
			ExpandBoolAsExecs = "ReturnValue"
		)
	)
	bool GetValidWaveSolverByClass(
		UPARAM(meta = (AllowAbstract = "false")) TSubclassOf<UOceanologyWaveSolverComponent> InWaveSolverClass,
		UOceanologyWaveSolverComponent*& OutWaveSolver
	) const;

	UFUNCTION(BlueprintPure, Category="Waves")
	virtual FOceanologyWaveInfo GetWaveInfoAtLocation(const FVector& Location);

#if WITH_EDITOR
	void OnWaveSolverPropertyChanged();
#endif

public:
	// UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = QuadTree)
	// FOceanologyAnyWaterQuadTreeSettings QuadTreeSettings;

	virtual FOceanologyWaterQuadTreeSettings GetQuadTreeSettings();

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
	bool UserOverrideMaterial = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material",
		meta = (EditCondition = "UserOverrideMaterial"))
	TObjectPtr<UMaterialInterface> Material = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material",
		meta = (EditCondition = "UserOverrideMaterial"))
	TObjectPtr<UMaterialInterface> MaterialFar = nullptr;

private:
	UPROPERTY(Category="Material", AdvancedDisplay, VisibleInstanceOnly, Transient, NonPIEDuplicateTransient,
		TextExportTransient)
	TObjectPtr<UMaterialInstanceDynamic> WaterMID;

	UPROPERTY(Category="Material", AdvancedDisplay, VisibleInstanceOnly, Transient, NonPIEDuplicateTransient,
		TextExportTransient)
	TObjectPtr<UMaterialInstanceDynamic> WaterFarDistanceMID;

public:
	UFUNCTION(BlueprintNativeEvent, Category="Material")
	void InitSurface();

	UFUNCTION(BlueprintCallable, Category="Material")
	virtual void SetVectorParameterValue(const FName ParameterName, const FLinearColor Value);

	UFUNCTION(BlueprintCallable, Category="Material")
	virtual void SetScalarParameterValue(const FName ParameterName, const float Value);

	UFUNCTION(BlueprintCallable, Category="Material")
	virtual void SetTextureParameterValue(const FName ParameterName, UTexture* Value);

	UFUNCTION(BlueprintCallable, Category="Material")
	virtual void UpdateWaterMaterialParameters(
		UMaterialInstanceDynamic* MID,
		EOceanologyUpdateWaterMaterialParameters UpdateWaterMaterialParameters
	) const;

	UFUNCTION(BlueprintCallable, Category="Material")
	virtual void SetWaterMaterialParameters(UMaterialInstanceDynamic* MID) const;

	UFUNCTION(BlueprintCallable, Category="Niagara")
	void UpdateWaterNiagaraVariables(
		UNiagaraComponent* Niagara,
		EOceanologyUpdateWaterMaterialParameters UpdateWaterMaterialParameters
	) const;

	UFUNCTION(BlueprintCallable, Category="Niagara")
	void SetWaterNiagaraVariables(UNiagaraComponent* Niagara) const;

	virtual UMaterialInterface* GetDefaultMaterial();
	virtual UMaterialInterface* GetDefaultFarMaterial();
	virtual UMaterialInterface* GetDefaultUnderwaterFogMaterial();
	virtual UMaterialInterface* GetDefaultUnderwaterColorlessMaterial();
	virtual UMaterialInterface* GetDefaultUnderwaterMeshMaterial();
	virtual UMaterialInterface* GetDefaultVolumetricFogMaterial();
	virtual UMaterialInterface* GetDefaultBubblesMaterial();
	virtual UMaterialInterface* GetDefaultShorelineWetnessMaterial();

	UFUNCTION(BlueprintCallable, Category="Material")
	void CreateOrUpdateWaterMID();

	UFUNCTION(BlueprintCallable, Category="Material")
	UMaterialInstanceDynamic* GetWaterMID();

	UFUNCTION(BlueprintCallable, Category="Material")
	void CreateOrUpdateWaterFarDistanceMID();

	UFUNCTION(BlueprintCallable, Category="Material")
	UMaterialInstanceDynamic* GetWaterFarDistanceMID();

	// UFUNCTION(BlueprintCallable, Category="Material")
	virtual UMaterialInstanceDynamic* GetCausticsMID();

public:
	/** Turning on/off debug messages of the water. WARNING: Debugging affects performance, do NOT use in production! */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category="WaterParent|Debug")
	bool DebugEnabled = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "WaterParent|Manager")
	TObjectPtr<AOceanologyManager> OceanologyManager = nullptr;

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shoreline Wetness")
	UMaterialInterface* ShorelineWetnessMaterial = nullptr;

private:
	UPROPERTY(Category="Material", AdvancedDisplay, VisibleInstanceOnly, Transient, NonPIEDuplicateTransient,
		TextExportTransient)
	TObjectPtr<UMaterialInstanceDynamic> ShorelineWetnessMID;

public:
	UFUNCTION(BlueprintNativeEvent, Category="Shoreline Wetness")
	void InitShorelineWetness();

	UFUNCTION(BlueprintCallable, Category="Material")
	void CreateOrUpdateShorelineWetnessMID();

	UFUNCTION(BlueprintCallable, Category="Material")
	UMaterialInstanceDynamic* GetShorelineWetnessMID();

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Surface Scattering", Replicated,
		ReplicatedUsing=OnRep_SurfaceScattering, meta=(PresetGroup="Color"))
	FOceanologySurfaceScattering SurfaceScattering;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Caustics", ReplicatedUsing=OnRep_Caustics,
		meta=(PresetGroup="Caustics"))
	FOceanologyCaustics Caustics;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caustics", meta=(PresetGroup="Caustics"))
	bool EnableCausticsOnGround = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Caustics", ReplicatedUsing=OnRep_GroundCaustics,
		meta=(PresetGroup="Caustics"))
	FOceanologyGroundCaustics GroundCaustics;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Refraction", Replicated,
		ReplicatedUsing=OnRep_Refraction, meta=(PresetGroup="Color"))
	FOceanologyRefraction Refraction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Normal Calculation", Replicated,
		ReplicatedUsing=OnRep_HorizonCorrection, meta=(PresetGroup="Detail"))
	FOceanologyHorizonCorrection HorizonCorrection;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Normal Calculation", Replicated,
		ReplicatedUsing=OnRep_Flipbook, meta=(PresetGroup="Detail"))
	FOceanologyFlipbook Flipbook;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Normal Calculation", Replicated,
		ReplicatedUsing=OnRep_ComputeNormals, meta=(PresetGroup="Detail"))
	FOceanologyComputeNormals ComputeNormals;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Surface|Camera", Replicated,
		ReplicatedUsing=OnRep_CameraOffset, meta=(PresetGroup="Detail"))
	FOceanologyCameraOffset CameraOffset;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Foam", ReplicatedUsing=OnRep_Foam, meta=(PresetGroup="Foam"))
	FOceanologyFoam Foam;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Procedural", ReplicatedUsing=OnRep_Procedural,
		meta=(PresetGroup="Detail"))
	FOceanologyProcedural Procedural;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="RVT", ReplicatedUsing=OnRep_RVT)
	FOceanologyRVT RVT;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Masking System", ReplicatedUsing=OnRep_Mask)
	FOceanologyMask Mask;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="WaterProjection", Replicated,
		ReplicatedUsing=OnRep_WaterProjection, meta=(PresetGroup="Detail"))
	FOceanologyWaterProjection WaterProjection;

	UPROPERTY(ReplicatedUsing=OnRep_ActorHeight)
	FOceanologyActorHeight ActorHeight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="GGX", ReplicatedUsing=OnRep_GGX, meta=(PresetGroup="Color"))
	FOceanologyGGX GGX;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shoreline Wetness", Replicated,
		meta=(PresetGroup="Wetness"))
	bool EnableWetness = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shoreline Wetness", ReplicatedUsing=OnRep_Wetness,
		meta=(PresetGroup="Wetness"))
	FOceanologyWetness Wetness;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fluid Simulation", ReplicatedUsing=OnRep_FluidSimulation)
	FOceanologyFluidSimulation FluidSimulation;

public:
	UFUNCTION()
	void OnRep_SurfaceScattering();

	UFUNCTION()
	void OnRep_Caustics();

	UFUNCTION()
	void OnRep_GroundCaustics();

	UFUNCTION()
	void OnRep_Refraction();

	UFUNCTION()
	void OnRep_HorizonCorrection();

	UFUNCTION()
	void OnRep_Flipbook();

	UFUNCTION()
	void OnRep_ComputeNormals();

	UFUNCTION()
	void OnRep_CameraOffset();

	UFUNCTION()
	void OnRep_Foam();

	UFUNCTION()
	void OnRep_Procedural();

	UFUNCTION()
	void OnRep_RVT();

	UFUNCTION()
	void OnRep_Mask();

	UFUNCTION()
	void OnRep_WaterProjection();

	UFUNCTION()
	void OnRep_ActorHeight();

	UFUNCTION()
	void OnRep_GGX();

	UFUNCTION()
	void OnRep_Wetness();

	UFUNCTION()
	void OnRep_FluidSimulation();
};
