// Fill out your copyright notice in the Description page of Project Settings.


#include "ShaderUtils/OceanologyCalculateWaveOffsetUtils.h"

FVector UOceanologyCalculateWaveOffsetUtils::CalculateWaveOffset(
	const FVector& ShoreWaves,
	const float SDFShoreline,
	const FVector& OceanWaves,
	const float SDFOcean
)
{
	const FVector TransitionOffset = ShoreWaves * SDFShoreline + OceanWaves * SDFOcean;
	return TransitionOffset;
}
