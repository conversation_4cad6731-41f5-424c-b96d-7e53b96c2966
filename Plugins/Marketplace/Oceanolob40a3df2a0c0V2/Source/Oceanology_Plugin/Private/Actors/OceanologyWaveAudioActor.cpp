// Fill out your copyright notice in the Description page of Project Settings.


#include "Actors/OceanologyWaveAudioActor.h"
#include "Components/OceanAudioComponent.h"

AOceanologyWaveAudio::AOceanologyWaveAudio()
{
	PrimaryActorTick.bCanEverTick = true;

	DefaultSceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("DefaultSceneRoot"));
	DefaultSceneRoot->SetMobility(EComponentMobility::Movable);
	RootComponent = DefaultSceneRoot;
	DefaultSceneRoot->CreationMethod = EComponentCreationMethod::Native;

	OceanAudioWaveComponent = CreateDefaultSubobject<UOceanAudioComponent>("OceanAudioWave");
	OceanAudioWaveComponent->Mode = EOceanAudioMode::Wave;
	OceanAudioWaveComponent->SetupAttachment(DefaultSceneRoot);

	bEnableAutoLODGeneration = false;
#if WITH_EDITOR
	bIsSpatiallyLoaded = false;
#endif
}
