// Fill out your copyright notice in the Description page of Project Settings.

#include "Actors/OceanologyLightSourceControllerActor.h"
#include "OceanologyRuntimeSettings.h"
#include "Actors/OceanologyInfiniteOceanActor.h"
#include "Actors/OceanologyWaterParentActor.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/Wave/OceanologyGerstnerWaveSolverComponent.h"
#include "Engine/DirectionalLight.h"
#include "Kismet/GameplayStatics.h"
#include "Utils/OceanologyMessageUtils.h"
#include "Utils/OceanologyWaterUtils.h"

AOceanologyLightSourceController::AOceanologyLightSourceController()
{
	PrimaryActorTick.bCanEverTick = true;

	DefaultSceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("DefaultSceneRoot"));
	DefaultSceneRoot->SetMobility(EComponentMobility::Movable);
	RootComponent = DefaultSceneRoot;
	DefaultSceneRoot->CreationMethod = EComponentCreationMethod::Native;

	bReplicates = false;
	bEnableAutoLODGeneration = false;
#if WITH_EDITOR
	bIsSpatiallyLoaded = false;
#endif
}

void AOceanologyLightSourceController::Init()
{
	if (!OceanologyWater)
	{
		OceanologyWater = Cast<AOceanologyInfiniteOcean>(
			UGameplayStatics::GetActorOfClass(this, AOceanologyInfiniteOcean::StaticClass())
		);
	}

	if (!OceanologyWater)
	{
		UOceanologyMessageUtils::CreateMessage(
			this,
			"OceanologyWater is not set! The light source controller will NOT work!",
			false
		);
		return;
	}

	if (!DirectionalLight)
	{
		UOceanologyMessageUtils::CreateMessage(
			this,
			"DirectionalLight is not set! The light source controller will NOT work!",
			false
		);
		return;
	}

	if (!LightFunctionMaterials.IsEmpty())
	{
		DirectionalLightComponentTags.Empty();

		for (auto [ComponentTagName, Material] : LightFunctionMaterials)
		{
			DirectionalLightComponentTags.Add(ComponentTagName);
		}
	}

	LightFunctionMIDs.Empty();

	if (DirectionalLightComponentTags.IsEmpty())
	{
		UOceanologyMessageUtils::CreateMessage(
			this,
			"DirectionalLight tags are not configured! The light source controller will NOT work!",
			false
		);
		return;
	}

	for (const FName DirectionalLightComponentTag : DirectionalLightComponentTags)
	{
		UMaterialInterface* LightFunctionMaterial = GetLightFunctionMaterial(DirectionalLightComponentTag);

		for (UActorComponent* ComponentByTag : DirectionalLight->GetComponentsByTag(
			     UDirectionalLightComponent::StaticClass(),
			     DirectionalLightComponentTag
		     ))
		{
			if (UDirectionalLightComponent* DirectionalLightComponent = Cast<
				UDirectionalLightComponent>(ComponentByTag))
			{
				UMaterialInstanceDynamic* LightFunctionMID = nullptr;

				LightFunctionMID = FOceanologyWaterUtils::GetOrCreateTransientMID(
					LightFunctionMID,
					FName(DirectionalLightComponentTag.ToString() + "_LightFunctionMID"),
					LightFunctionMaterial,
					FOceanologyWaterUtils::GetTransientMIDFlags()
				);

				SetMaterialParameters(LightFunctionMID);

				DirectionalLightComponent->SetLightFunctionMaterial(LightFunctionMID);

				LightFunctionMIDs.Add(LightFunctionMID);
			}
		}
	}
}

void AOceanologyLightSourceController::ReloadSettings(const bool Silent)
{
	Init();

	if (!Silent)
	{
		UOceanologyMessageUtils::CreateMessage(this, "Settings reloaded successfully!", true);
	}
}

UMaterialInterface* AOceanologyLightSourceController::GetLightFunctionMaterial(FName DirectionalLightComponentTag)
{
	UMaterialInterface* LightFunctionMaterial = nullptr;

	if (const FOceanologyLightFunctionMaterial* Find = LightFunctionMaterials.FindByPredicate(
		[&DirectionalLightComponentTag](const FOceanologyLightFunctionMaterial& InItem)
		{
			return InItem.ComponentTagName == DirectionalLightComponentTag;
		}); Find)
	{
		LightFunctionMaterial = Find->Material;
	}

	if (!LightFunctionMaterial)
	{
		LightFunctionMaterial = GetDefault<UOceanologyRuntimeSettings>()->GetDirectionalLightMaterial();
	}

	return LightFunctionMaterial;
}

void AOceanologyLightSourceController::SetMaterialParameters(UMaterialInstanceDynamic* LightFunctionMID)
{
	if (!OceanologyWater || !DirectionalLight)
	{
		return;
	}

	OceanologyWater->SetWaterMaterialParameters(LightFunctionMID);

	if (UOceanologyWaveSolverComponent* WaveSolverComponent = OceanologyWater->GetWaveSolver())
	{
		UOceanologyShoreWavesHelper::SetMaterialParameters(
			LightFunctionMID,
			WaveSolverComponent->ShoreWaves
		);

		if (WaveSolverComponent->IsA(UOceanologyGerstnerWaveSolverComponent::StaticClass()))
		{
			UOceanologyGerstnerWaveSolverComponent* GerstnerWaveSolver = Cast<UOceanologyGerstnerWaveSolverComponent>(
				WaveSolverComponent
			);

			UOceanologySpectralGerstnerHelper::SetMaterialParameters(
				LightFunctionMID,
				GerstnerWaveSolver->SpectralGerstner
			);
		}
	}
}

#if WITH_EDITOR
void AOceanologyLightSourceController::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	if (PropertyChangedEvent.GetPropertyName() == GET_MEMBER_NAME_CHECKED(
		AOceanologyLightSourceController, LightFunctionMaterials))
	{
		TArray<FOceanologyLightFunctionMaterial> LocalLightFunctionMaterials;
		for (FOceanologyLightFunctionMaterial LightFunctionMaterial : LightFunctionMaterials)
		{
			if (!LightFunctionMaterial.Material)
			{
				LightFunctionMaterial.Material = GetDefault<UOceanologyRuntimeSettings>()->
					GetDirectionalLightMaterial();
			}

			LocalLightFunctionMaterials.Add(LightFunctionMaterial);
		}

		PreEditChange(nullptr);
		LightFunctionMaterials = LocalLightFunctionMaterials;
		PostEditChange();
		Modify();
	}

	Super::PostEditChangeProperty(PropertyChangedEvent);
}
#endif

void AOceanologyLightSourceController::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);

	Init();

	if (OceanologyWater)
	{
#if WITH_EDITOR
		OceanologyWater->OnPropertyUpdated.RemoveAll(this);
		OceanologyWater->OnPropertyUpdated.AddUObject(this, &AOceanologyLightSourceController::PostEditChange);
#endif
	}
}

void AOceanologyLightSourceController::BeginPlay()
{
	Super::BeginPlay();

	Init();
}
