// Copyright 1998-2023 Epic Games, Inc. All Rights Reserved.
// =================================================
// Created by: Galidar
// Project name: Oceanology
// Created on: 2023/12/15
//
// =================================================


#include "Actors/OceanologyManagerActor.h"
#include "Actors/OceanologyInfiniteOceanActor.h"
#include "Landscape.h"
#include "OceanologyRuntimeSettings.h"
#include "Actors/OceanologyWaterParentActor.h"
#include "Async/TaskGraphInterfaces.h"
#include "Components/BoxComponent.h"
#include "Components/OceanologyGameTimeComponent.h"
#include "Components/OceanologyHeightmapComponent.h"
#include "Components/RuntimeVirtualTextureComponent.h"
#include "Engine/RendererSettings.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMaterialLibrary.h"
#include "Kismet/KismetRenderingLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Engine/World.h"
#include "Materials/Material.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Utils/OceanologyMessageUtils.h"
#include "VT/VirtualTexture.h"
#include "VT/RuntimeVirtualTexture.h"
#include "VT/VirtualTextureBuilder.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(OceanologyManagerActor)

AOceanologyManager::AOceanologyManager(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	DefaultSceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("DefaultSceneRoot"));
	DefaultSceneRoot->SetMobility(EComponentMobility::Movable);
	RootComponent = DefaultSceneRoot;
	DefaultSceneRoot->CreationMethod = EComponentCreationMethod::Native;

	HeightmapVirtualTextureComponent = CreateDefaultSubobject<UOceanologyVirtualTextureComponent>(
		TEXT("HeightmapVirtualTexture")
	);
	HeightmapVirtualTextureComponent->SetupAttachment(DefaultSceneRoot);

	HeightmapComponent = CreateDefaultSubobject<UOceanologyHeightmapComponent>(TEXT("Heightmap"));
	GameTimeComponent = CreateDefaultSubobject<UOceanologyGameTimeComponent>(TEXT("GameTime"));

	Box = CreateDefaultSubobject<UBoxComponent>(TEXT("Bounds"));
	Box->SetupAttachment(DefaultSceneRoot);
	Box->SetBoxExtent(FVector(.5f, .5f, .5f), false);
	Box->SetRelativeTransform(FTransform(FVector(.5f, .5f, .5f)));
	Box->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	Box->SetCanEverAffectNavigation(false);
	Box->CanCharacterStepUpOn = ECB_No;
	Box->SetGenerateOverlapEvents(false);

	bEnableAutoLODGeneration = false;
#if WITH_EDITOR
	bIsSpatiallyLoaded = false;
#endif
}

void AOceanologyManager::SetBoundsAlignActor()
{
#if WITH_EDITOR
	PreEditChange(nullptr);

	HeightmapVirtualTextureComponent->PreEditChange(nullptr);
	HeightmapVirtualTextureComponent->SetBoundsAlignActor(BoundsAlignActor.LoadSynchronous());
	HeightmapVirtualTextureComponent->PostEditChange();

	PostEditChange();
#endif
}

bool AOceanologyManager::IsRVTSupported() const
{
	static const auto CVarVirtualTexture = IConsoleManager::Get().
		FindTConsoleVariableDataInt(TEXT("r.VirtualTextures"));

	if (CVarVirtualTexture &&
		CVarVirtualTexture->GetValueOnAnyThread() == 0)
	{
		UOceanologyMessageUtils::CreateMessage(this, "Virtual Textures are disabled in the project settings! Water Depth and Shore Waves will NOT work!", false);
		return false;
	}

	if (!UseVirtualTexturing(GetFeatureLevelShaderPlatform(GetWorld()->GetFeatureLevel())))
	{
		UOceanologyMessageUtils::CreateMessage(this, "Virtual Textures are not supported on this platform! Water Depth and Shore Waves will NOT work!", false);
		return false;
	}

	if (!GetDefault<URendererSettings>()->bEnableVirtualTexturePostProcessing)
	{
		UOceanologyMessageUtils::CreateMessage(
			this,
			"VirtualTexturePostProcessing is disabled in the project settings! Underwater will NOT work!",
			false
		);
		return false;
	}

	return true;
}

void AOceanologyManager::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);

#if WITH_EDITOR
	const UOceanologyRuntimeSettings* OceanologyRuntimeSettings = GetDefault<UOceanologyRuntimeSettings>();

	if (!HeightmapVirtualTextureComponent->GetVirtualTexture())
	{
		HeightmapVirtualTextureComponent->SetVirtualTexture(OceanologyRuntimeSettings->GetRVT_Heightmap());
	}

	if (!HeightmapVirtualTextureComponent->GetStreamingTexture())
	{
		HeightmapVirtualTextureComponent->SetStreamingTexture(OceanologyRuntimeSettings->GetRVT_Heightmap_Builder());
	}
#endif

	Init();
}

#if WITH_EDITOR
void AOceanologyManager::PostEditMove(bool bFinished)
{
	Super::PostEditMove(bFinished);
	
	HeightmapComponent->AutoConfigureHeightmap();
}

void AOceanologyManager::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.GetPropertyName() == GET_MEMBER_NAME_CHECKED(AOceanologyManager, BoundsAlignActor))
	{
		SetBoundsAlignActor();
	}
}
#endif

void AOceanologyManager::BeginPlay()
{
	Super::BeginPlay();

	Init();
}

void AOceanologyManager::Init()
{
	if (!IsRVTSupported())
	{
		return;
	}

	if (!HeightmapVirtualTextureComponent->GetVirtualTexture())
	{
		UOceanologyMessageUtils::CreateMessage(this, "Virtual Texture is NOT configured!", false);
		return;
	}

	HeightmapComponent->Init(this);
	HeightmapComponent->AutoConfigureHeightmap();
}
