// Copyright 1998-2023 Epic Games, Inc. All Rights Reserved.
// =================================================
// Created by: Galidar
// Project name: Oceanology
// Created on: 2023/12/15
//
// =================================================

#include "Components/OceanologyUnderwaterComponent.h"
#include "NiagaraComponent.h"
#include "Actors/OceanologyWaterParentActor.h"
#include "Components/PostProcessComponent.h"
#include "Utils/OceanologyWaterUtils.h"
#include "LandscapeProxy.h"
#include "Landscape.h"
#include "Kismet/KismetMaterialLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Net/UnrealNetwork.h"
#include "Structs/OceanologyBubbles.h"
#include "Structs/OceanologyRVT.h"
#include "Utils/OceanologyMessageUtils.h"
#include "NiagaraSystem.h"
#include "OceanologyRuntimeSettings.h"
#include "Actors/OceanologyManagerActor.h"
#include "Components/BoxComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Net/Core/PushModel/PushModel.h"
#include "Structs/OceanologyShoreWaves.h"
#include "Utils/OceanologyNiagaraUtils.h"
#include "Landscape.h"

UOceanologyUnderwaterComponent::UOceanologyUnderwaterComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	bAutoActivate = true;
	bTickInEditor = true;
}

void UOceanologyUnderwaterComponent::Init()
{
	if (!GetOwner()->IsA(AOceanologyWaterParent::StaticClass()))
	{
		return;
	}

	OceanologyWater = Cast<AOceanologyWaterParent>(GetOwner());
	MARK_PROPERTY_DIRTY_FROM_NAME(UOceanologyUnderwaterComponent, OceanologyWater, this);

	if (!OceanologyWater
		|| !OceanologyWater->DefaultSceneRoot
		|| !OceanologyWater->UnderwaterBoxVolumeComponent
		|| !OceanologyWater->UnderwaterPostProcessComponent)
	{
		return;
	}

	// Clear ALL.
	{
		OceanologyWater->UnderwaterPostProcessComponent->Settings.WeightedBlendables.Array.Empty();

		UnderwaterFogMID = nullptr;
		UnderwaterDistortionMID = nullptr;
		VolumetricFogMID = nullptr;
		BubblesMID = nullptr;

		if (!UserOverrideUnderwaterMaterial)
		{
			UnderwaterFogMaterial = nullptr;
			UnderwaterDistortionMaterial = nullptr;
			VolumetricFogMaterial = nullptr;
		}

		OceanologyWater->UnderwaterPostProcessComponent->bEnabled = false;
		OceanologyWater->UnderwaterFogMeshComponent->SetVisibility(false, false);
	}

	if (UnderwaterMode == EOceanologyUnderwaterMode::None)
	{
		OceanologyWater->UnderwaterPostProcessComponent->bEnabled = false;
		OceanologyWater->UnderwaterFogMeshComponent->SetVisibility(false, false);
		return;
	}

	// Distortion
	{
		if (!UserOverrideUnderwaterMaterial || !UnderwaterDistortionMaterial)
		{
			UnderwaterDistortionMaterial = OceanologyWater->GetDefaultUnderwaterColorlessMaterial();
		}

		if (!UnderwaterDistortionMaterial)
		{
			UOceanologyMessageUtils::CreateMessage(this, "Underwater Material is null! Cannot be initialized!", false);
			return;
		}

		CreateOrUpdateUnderwaterDistortionMID();
		OnRep_UnderwaterDistortion();
		OceanologyWater->UnderwaterPostProcessComponent->bEnabled = true;
		OceanologyWater->UnderwaterPostProcessComponent->AddOrUpdateBlendable(GetUnderwaterDistortionMID(), 1.0);
	}

	switch (UnderwaterMode)
	{
	case EOceanologyUnderwaterMode::Underwater:
		{
			if (!UserOverrideUnderwaterMaterial || !UnderwaterFogMaterial)
			{
				UnderwaterFogMaterial = OceanologyWater->GetDefaultUnderwaterFogMaterial();
			}

			if (!UserOverrideUnderwaterMaterial)
			{
				VolumetricFogMaterial = nullptr;
			}

			if (!UnderwaterFogMaterial)
			{
				UOceanologyMessageUtils::CreateMessage(this, "Underwater Material is null! Cannot be initialized!",
				                                       false);
				return;
			}

			CreateOrUpdateUnderwaterFogMID();
			OnRep_UnderwaterFog();
			OceanologyWater->UnderwaterFogMeshComponent->SetMaterial(0, GetUnderwaterFogMID());
			OceanologyWater->UnderwaterFogMeshComponent->bReverseCulling = true;
			OceanologyWater->UnderwaterFogMeshComponent->SetVisibility(true, false);
		}

		break;

	case EOceanologyUnderwaterMode::VolumetricFog:
		{
			if (!UserOverrideUnderwaterMaterial || !VolumetricFogMaterial)
			{
				VolumetricFogMaterial = OceanologyWater->GetDefaultVolumetricFogMaterial();
			}

			if (!VolumetricFogMaterial)
			{
				UOceanologyMessageUtils::CreateMessage(
					this,
					"Underwater Material is null! Cannot be initialized!",
					false
				);
				return;
			}

			CreateOrUpdateVolumetricFogMID();
			OnRep_VolumetricFog();
			OceanologyWater->UnderwaterFogMeshComponent->SetMaterial(0, GetVolumetricFogMID());
			OceanologyWater->UnderwaterFogMeshComponent->bReverseCulling = false;
			OceanologyWater->UnderwaterFogMeshComponent->SetVisibility(true, false);
		}

		break;

	default: ;
	}

	InitBubbles();
}

void UOceanologyUnderwaterComponent::SetWaterPresetResult(
	const FOceanologyWaterPresetResult& WaterPresetResult
)
{
	if (!OceanologyWater || !OceanologyWater->HasAuthority())
	{
		return;
	}

	UnderwaterFog = WaterPresetResult.UnderwaterFog;
	UnderwaterDistortion = WaterPresetResult.UnderwaterDistortion;
	VolumetricFog = WaterPresetResult.VolumetricFog;
	BubblesSettings = WaterPresetResult.BubblesSettings;

	MARK_PROPERTY_DIRTY_FROM_NAME(UOceanologyUnderwaterComponent, UnderwaterFog, this);
	MARK_PROPERTY_DIRTY_FROM_NAME(UOceanologyUnderwaterComponent, UnderwaterDistortion, this);
	MARK_PROPERTY_DIRTY_FROM_NAME(UOceanologyUnderwaterComponent, VolumetricFog, this);
	MARK_PROPERTY_DIRTY_FROM_NAME(UOceanologyUnderwaterComponent, BubblesSettings, this);

	OnRep_UnderwaterFog();
	OnRep_UnderwaterDistortion();
	OnRep_VolumetricFog();
	OnRep_BubbleSettings();
}


void UOceanologyUnderwaterComponent::OnRep_UnderwaterFog()
{
	if (!UnderwaterFogMID)
	{
		return;
	}

	OceanologyWater->SetWaterMaterialParameters(GetUnderwaterFogMID());
	UOceanologyUnderwaterFogHelper::SetMaterialParameters(GetUnderwaterFogMID(), UnderwaterFog);
}

void UOceanologyUnderwaterComponent::CreateOrUpdateUnderwaterFogMID()
{
	UnderwaterFogMID = FOceanologyWaterUtils::GetOrCreateTransientMID(
		UnderwaterFogMID,
		TEXT("UnderwaterFogMID"),
		UnderwaterFogMaterial,
		FOceanologyWaterUtils::GetTransientMIDFlags()
	);
}

UMaterialInstanceDynamic* UOceanologyUnderwaterComponent::GetUnderwaterFogMID()
{
	CreateOrUpdateUnderwaterFogMID();

	return UnderwaterFogMID;
}

void UOceanologyUnderwaterComponent::OnRep_UnderwaterDistortion()
{
	if (!UnderwaterDistortionMID)
	{
		return;
	}

	OceanologyWater->SetWaterMaterialParameters(GetUnderwaterDistortionMID());
	UOceanologyUnderwaterDistortionHelper::SetMaterialParameters(GetUnderwaterDistortionMID(), UnderwaterDistortion);
}

void UOceanologyUnderwaterComponent::CreateOrUpdateUnderwaterDistortionMID()
{
	UnderwaterDistortionMID = FOceanologyWaterUtils::GetOrCreateTransientMID(
		UnderwaterDistortionMID,
		TEXT("UnderwaterDistortionMID"),
		UnderwaterDistortionMaterial,
		FOceanologyWaterUtils::GetTransientMIDFlags()
	);
}

UMaterialInstanceDynamic* UOceanologyUnderwaterComponent::GetUnderwaterDistortionMID()
{
	CreateOrUpdateUnderwaterDistortionMID();

	return UnderwaterDistortionMID;
}

void UOceanologyUnderwaterComponent::OnRep_VolumetricFog()
{
	if (!VolumetricFogMID)
	{
		return;
	}

	OceanologyWater->SetWaterMaterialParameters(GetVolumetricFogMID());
	UOceanologyVolumetricFogHelper::SetMaterialParameters(GetVolumetricFogMID(), VolumetricFog);
}

void UOceanologyUnderwaterComponent::CreateOrUpdateVolumetricFogMID()
{
	VolumetricFogMID = FOceanologyWaterUtils::GetOrCreateTransientMID(
		VolumetricFogMID,
		TEXT("VolumetricFogMID"),
		VolumetricFogMaterial,
		FOceanologyWaterUtils::GetTransientMIDFlags()
	);
}

UMaterialInstanceDynamic* UOceanologyUnderwaterComponent::GetVolumetricFogMID()
{
	CreateOrUpdateVolumetricFogMID();

	return VolumetricFogMID;
}


void UOceanologyUnderwaterComponent::OnRep_BubbleSettings()
{
	if (!BubblesMID)
	{
		return;
	}

	UOceanologyBubblesHelper::SetMaterialParameters(GetBubblesMID(), BubblesSettings.Bubbles);
}

void UOceanologyUnderwaterComponent::InitBubbles()
{
	if (OceanologyWater->BubblesComponent)
	{
		OceanologyWater->BubblesComponent->DestroyInstance();
	}

	switch (BubblesSettings.EnableBubbles)
	{
	case Enabled:
		OceanologyWater->BubblesComponent->Activate();
		OceanologyWater->BubblesComponent->SetVisibility(true, false);
		break;

	case EnabledOnInteraction:
		OceanologyWater->BubblesComponent->SetVisibility(false, false);
		OceanologyWater->BubblesComponent->Deactivate();
		break;

	case Disabled:
		OceanologyWater->BubblesComponent->SetVisibility(false, false);
		OceanologyWater->BubblesComponent->Deactivate();
		return;

	default:
		ensureMsgf(false, TEXT("Unsupported bubble activation mode!"));
	}

	if (!BubblesSettings.BubblesEffect)
	{
		BubblesSettings.BubblesEffect = OceanologyWater->GetDefaultBubblesEffect();
	}

	if (!BubblesSettings.BubblesMaterial)
	{
		BubblesSettings.BubblesMaterial = OceanologyWater->GetDefaultBubblesMaterial();
	}

	CreateOrUpdateBubblesMID();

	UOceanologyBubblesHelper::SetMaterialParameters(GetBubblesMID(), BubblesSettings.Bubbles);

	BubblesSettings.VolumeScale = OceanologyWater->UnderwaterBoxVolumeComponent->GetScaledBoxExtent();

	if (ALandscapeProxy* LandscapeProxy = OceanologyWater->FindLandscape())
	{
		// Get the main ALandscape from the LandscapeProxy
		ALandscape* PrimaryLandscape = LandscapeProxy->GetLandscapeActor();
		if (PrimaryLandscape)
		{
			// Set the landscape source for the Niagara bubbles component
			UOceanologyNiagaraUtils::SetSourceLandscape(
				OceanologyWater->BubblesComponent,
				TEXT("Landscape"),
				PrimaryLandscape
			);
		}
	}

	
	OceanologyWater->BubblesComponent->SetAsset(BubblesSettings.BubblesEffect.LoadSynchronous());
	OceanologyWater->BubblesComponent->SetVariableBool("PositionToLocalSpace", BubblesSettings.PositionToLocalSpace);
	OceanologyWater->BubblesComponent->SetVariableFloat("SpawnRate", BubblesSettings.SpawnRate);
	OceanologyWater->BubblesComponent->SetVariableVec3("VolumeScale", BubblesSettings.VolumeScale);
	OceanologyWater->BubblesComponent->SetVariableFloat("DisplacementFactor", BubblesSettings.DisplacementFactor);
	OceanologyWater->BubblesComponent->SetVariableFloat("NormalFactor", BubblesSettings.NormalFactor);
	OceanologyWater->BubblesComponent->SetVariableFloat("GravityForce", BubblesSettings.GravityForce);
	OceanologyWater->BubblesComponent->SetVariableFloat("SpriteSizeMax", BubblesSettings.SpriteSizeMax);
	OceanologyWater->BubblesComponent->SetVariableFloat("SpriteSizeMin", BubblesSettings.SpriteSizeMin);
	OceanologyWater->BubblesComponent->SetVariableMaterial("BubblesMaterial", GetBubblesMID());

	OceanologyWater->SetWaterNiagaraVariables(OceanologyWater->BubblesComponent);

	OceanologyWater->BubblesComponent->SetVariableFloat("WaterHeight", OceanologyWater->GetActorLocation().Z);
}

void UOceanologyUnderwaterComponent::CreateOrUpdateBubblesMID()
{
	BubblesMID = FOceanologyWaterUtils::GetOrCreateTransientMID(
		BubblesMID,
		TEXT("BubblesMID"),
		BubblesSettings.BubblesMaterial.LoadSynchronous(),
		FOceanologyWaterUtils::GetTransientMIDFlags()
	);
}

UMaterialInstanceDynamic* UOceanologyUnderwaterComponent::GetBubblesMID()
{
	CreateOrUpdateBubblesMID();

	return BubblesMID;
}


void UOceanologyUnderwaterComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	FDoRepLifetimeParams Parameters;
	Parameters.bIsPushBased = true;

	DOREPLIFETIME_WITH_PARAMS_FAST(UOceanologyUnderwaterComponent, OceanologyWater, Parameters);

	DOREPLIFETIME_WITH_PARAMS_FAST(UOceanologyUnderwaterComponent, UnderwaterFog, Parameters);
	DOREPLIFETIME_WITH_PARAMS_FAST(UOceanologyUnderwaterComponent, UnderwaterDistortion, Parameters);
	DOREPLIFETIME_WITH_PARAMS_FAST(UOceanologyUnderwaterComponent, VolumetricFog, Parameters);
	DOREPLIFETIME_WITH_PARAMS_FAST(UOceanologyUnderwaterComponent, BubblesSettings, Parameters);
}
