// Fill out your copyright notice in the Description page of Project Settings.


#include "Components/OceanWaterInteractorMeshComponent.h"
#include "Engine/StaticMesh.h"

UOceanWaterInteractorMeshComponent::UOceanWaterInteractorMeshComponent()
{
	UStaticMesh* SphereMesh = SphereMesh = LoadObject<UStaticMesh>(
		nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere")
	);
	if (!SphereMesh)
	{
		SphereMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/EngineMeshes/Sphere.Sphere"));
	}

	UStaticMeshComponent::SetStaticMesh(SphereMesh);
	UPrimitiveComponent::SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	UPrimitiveComponent::SetCollisionObjectType(ECC_WorldStatic);
	UPrimitiveComponent::SetCollisionResponseToAllChannels(ECR_Overlap);
	
	SetHiddenInGame(true);
}
