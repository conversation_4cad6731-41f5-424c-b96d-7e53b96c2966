// Copyright 1998-2023 Epic Games, Inc. All Rights Reserved.
// =================================================
// Created by: Galidar
// Project name: Oceanology
// Created on: 2023/12/15
//
// =================================================

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "OceanologyWaterSurfaceDebuggerActor.generated.h"

class AOceanologyWaterParent;

UCLASS()
class OCEANOLOGY_PLUGINEDITOR_API AOceanologyWaterSurfaceDebugger : public AActor
{
	GENERATED_BODY()

public:
	AOceanologyWaterSurfaceDebugger();

	UPROPERTY(BlueprintReadWrite, NonTransactional, meta = (Category = "Default"))
	TObjectPtr<USceneComponent> DefaultSceneRoot;

	/** The owner water of this water volume. Mandatory. Used to determine wave height. Physics & swimming logic's fundamental settings. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Settings")
	TObjectPtr<AOceanologyWaterParent> OceanologyWater = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Waves")
	int DebugWaterSurfaceGridCount = 16;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Waves")
	float DebugWaterSurfaceGridSize = 1000.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Waves")
	int DebugWaterSurfaceSphereSegments = 12;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Waves")
	float DebugWaterSurfaceSphereRadius = 100.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Waves")
	float DebugWaterSurfaceSphereThickness = 10.0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Waves")
	FLinearColor DebugWaterSurfaceSphereColor = FLinearColor::Red;

public:
	virtual void Tick(const float DeltaTime) override;

	virtual bool ShouldTickIfViewportsOnly() const override
	{
		return true;
	}

protected:
	virtual void OnConstruction(const FTransform& Transform) override;
	virtual void BeginPlay() override;
	void Init();

private:
	void GetSurfaceDebugPoints(TArray<FVector>& OutDebugPoints) const;
	void DrawDebugWaterSurface() const;

	UPROPERTY()
	TArray<FVector> DebugPoints;
};
