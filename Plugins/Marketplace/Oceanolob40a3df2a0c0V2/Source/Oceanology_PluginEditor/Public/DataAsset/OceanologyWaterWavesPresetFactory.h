#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "OceanologyWaterWavesPresetFactory.generated.h"

/**
 * This is an auto-generated class from WaterParent actor via AOceanologyPresetConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit WaterParent structs instead!
 * Generated at: 28 April 2025
 **/
UCLASS()
class OCEANOLOGY_PLUGINEDITOR_API UOceanologyWaterWavesPresetFactory : public UFactory
{
	GENERATED_BODY()

public:
	UOceanologyWaterWavesPresetFactory();

	virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
	virtual FText GetDisplayName() const override;
	virtual FString GetDefaultNewAssetName() const override;
};
