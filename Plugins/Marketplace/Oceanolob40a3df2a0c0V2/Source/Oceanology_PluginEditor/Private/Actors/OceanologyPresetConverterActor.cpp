// Fill out your copyright notice in the Description page of Project Settings.


#include "Actors/OceanologyPresetConverterActor.h"
#include "Actors/OceanologyInfiniteOceanActor.h"
#include "Actors/OceanologyWaterParentActor.h"
#include "Components/OceanologyUnderwaterComponent.h"
#include "Components/Wave/OceanologyWaveSolverComponent.h"
#include "Interfaces/IPluginManager.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"


AOceanologyPresetConverterActor::AOceanologyPresetConverterActor()
{
	PrimaryActorTick.bCanEverTick = true;

	bEnableAutoLODGeneration = false;
#if WITH_EDITOR
	bIsSpatiallyLoaded = false;
#endif
}

void AOceanologyPresetConverterActor::DumpPresetStructs()
{
	if (!Water)
	{
		return;
	}

	InitSourceDirectories();

	UKismetSystemLibrary::PrintString(
		this,
		"DumpPresetStructs",
		true,
		true,
		FLinearColor::Green,
		1.0
	);

	PresetGroups.Empty();
	PresetGroupNames.Empty();

	if (Water->WaveSolver)
	{
		ReadPresetGroups(Water->WaveSolver->GetClass());
	}

	ReadPresetGroups(Water->GetClass());
	if (Water->UnderwaterComponent)
	{
		ReadPresetGroups(Water->UnderwaterComponent->GetClass());
	}

	ParsePresetGroups();
}

void AOceanologyPresetConverterActor::InitSourceDirectories()
{
	StructsPublicSourceCodeDirectory = IPluginManager::Get()
	                                   .FindPlugin(TEXT("Oceanology_Plugin"))
	                                   ->GetBaseDir()
		/ TEXT("Source/Oceanology_Plugin/Public/Structs/");

	StructsPrivateSourceCodeDirectory = IPluginManager::Get()
	                                    .FindPlugin(TEXT("Oceanology_Plugin"))
	                                    ->GetBaseDir()
		/ TEXT("Source/Oceanology_Plugin/Private/Structs/");

	PresetsPublicSourceCodeDirectory = IPluginManager::Get()
	                                   .FindPlugin(TEXT("Oceanology_Plugin"))
	                                   ->GetBaseDir()
		/ TEXT("Source/Oceanology_Plugin/Public/DataAsset/");

	PresetsPrivateSourceCodeDirectory = IPluginManager::Get()
	                                    .FindPlugin(TEXT("Oceanology_Plugin"))
	                                    ->GetBaseDir()
		/ TEXT("Source/Oceanology_Plugin/Private/DataAsset/");

	PresetFactoryPublicSourceCodeDirectory = IPluginManager::Get()
	                                         .FindPlugin(TEXT("Oceanology_Plugin"))
	                                         ->GetBaseDir()
		/ TEXT("Source/Oceanology_PluginEditor/Public/DataAsset/");

	PresetFactoryPrivateSourceCodeDirectory = IPluginManager::Get()
	                                          .FindPlugin(TEXT("Oceanology_Plugin"))
	                                          ->GetBaseDir()
		/ TEXT("Source/Oceanology_PluginEditor/Private/DataAsset/");
}

void AOceanologyPresetConverterActor::ReadPresetGroups(UClass* Clazz)
{
	for (TFieldIterator<FProperty> PropIt(Clazz, EFieldIteratorFlags::IncludeSuper); PropIt; ++PropIt)
	{
		if (FProperty* Property = *PropIt)
		{
			if (!Property->GetMetaDataMap()->Contains("PresetGroup"))
			{
				continue;
			}

			FString PropertyType = "";
			FString TypeClass = "";

			if (Property->IsA(FStructProperty::StaticClass());
				const FStructProperty* StructProperty = CastField<FStructProperty>(Property))
			{
				PropertyType = "F" + StructProperty->Struct->GetName();
				TypeClass = "Structs/" + StructProperty->Struct->GetName();
			}
			if (Property->IsA(FEnumProperty::StaticClass());
				const FEnumProperty* EnumProperty = CastField<FEnumProperty>(Property))
			{
				PropertyType = EnumProperty->GetEnum()->GetName();
				TypeClass = "Enums/Oceanology" + Property->GetName();
			}
			else if (Property->IsA(FBoolProperty::StaticClass()))
			{
				PropertyType = "bool";
			}

			PresetGroups.Add(FOceanologyPresetGroup(
				Property->GetName(),
				PropertyType,
				TypeClass,
				Property->GetMetaData("PresetGroup"),
				Property->HasMetaData("PresetClassSource") ? Property->GetMetaData("PresetClassSource") : "WaterParent",
				Property->HasMetaData("PresetDefaultValue") ? Property->GetMetaData("PresetDefaultValue") : "None",
				Property->HasMetaData("PresetNoLerp") ? Property->GetMetaData("PresetNoLerp") : "false",
				Property->HasMetaData("EditCondition") ? Property->GetMetaData("EditCondition") : ""
			));
		}
	}

	for (FOceanologyPresetGroup PresetGroup : PresetGroups)
	{
		if (!PresetGroupNames.Contains(PresetGroup.PresetGroup))
		{
			PresetGroupNames.Add(PresetGroup.PresetGroup);
		}
	}
}

void AOceanologyPresetConverterActor::ParsePresetGroups()
{
	WriteWaterPresetResultStruct();
	WritePublicWaterPresetDataAsset();
	WritePrivateWaterPresetDataAsset();
	WriteDefaultPresetInclusionGroups();

	WritePublicGroupedWaterPresetsStruct();
	WritePrivateGroupedWaterPresetsStruct();
	WriteAllPublicGroupedWaterPresetDataAsset();
	WriteAllPrivateGroupedWaterPresetDataAsset();

	WriteAllPublicPresetFactory();
	WriteAllPrivatePresetFactory();
}

void AOceanologyPresetConverterActor::WriteWaterPresetResultStruct()
{
	const FString FilePath = StructsPublicSourceCodeDirectory + "OceanologyWaterPresetResult.h";
	FString FileContent = "";

	FileContent.Append("#pragma once\n\n");
	for (FOceanologyPresetGroup PresetGroup : PresetGroups)
	{
		if (!PresetGroup.TypeClass.IsEmpty())
		{
			FileContent.Append("#include \"").Append(PresetGroup.TypeClass).Append(".h\"\n");
		}
	}
	FileContent.Append("#include \"OceanologyWaterPresetResult.generated.h\"\n");
	FileContent.Append("\n\n");
	AppendHeader(FileContent);
	FileContent.Append("USTRUCT(BlueprintType)\n").Append(
		"struct FOceanologyWaterPresetResult\n{\n\tGENERATED_BODY()\n\n");

	for (FOceanologyPresetGroup PresetGroup : PresetGroups)
	{
		AppendProperty(FileContent, PresetGroup);
	}

	FileContent.Append("\n};\n");

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WritePublicWaterPresetDataAsset()
{
	const FString FilePath = PresetsPublicSourceCodeDirectory + "OceanologyWaterPreset.h";
	FString FileContent = "";

	AppendCommonPublicPreset("", FileContent);

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WritePrivateWaterPresetDataAsset()
{
	const FString FilePath = PresetsPrivateSourceCodeDirectory + "OceanologyWaterPreset.cpp";
	FString FileContent = "";

	AppendCommonPrivatePreset("", FileContent);

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WriteDefaultPresetInclusionGroups()
{
	const FString FilePath = StructsPublicSourceCodeDirectory + "OceanologyDefaultPresetInclusionGroups.h";
	FString FileContent = "";

	FileContent.Append("#pragma once\n\n");

	AppendHeader(FileContent);
	FileContent.Append("struct FOceanologyDefaultPresetInclusionGroups\n").Append("{\n").Append("public:\n");
	FileContent.Append("\tTArray<FName> DefaultPresetInclusionGroups = {\n");
	for (FOceanologyPresetGroup PresetGroup : PresetGroups)
	{
		FileContent.Append("\t\t\"").Append(PresetGroup.PropertyName).Append("\",\n");
	}
	FileContent.Append("\t};\n");
	FileContent.Append("};\n");
	FileContent.Append("\n\n");

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WritePublicGroupedWaterPresetsStruct()
{
	const FString FilePath = StructsPublicSourceCodeDirectory + "OceanologyGroupedWaterPresets.h";
	FString FileContent = "";

	FileContent.Append("#pragma once\n\n");

	for (FString PresetGroupName : PresetGroupNames)
	{
		FileContent.Append("#include \"DataAsset/OceanologyWater" + PresetGroupName + "Preset.h\"\n");
	}

	FileContent.Append("#include \"OceanologyGroupedWaterPresets.generated.h\"\n\n");

	FileContent.Append("class AOceanologyWaterParent;\n\n");

	AppendHeader(FileContent);
	FileContent.Append(
		"USTRUCT(BlueprintType)\nstruct FOceanologyGroupedWaterPresets\n{\n\tGENERATED_BODY()\n\npublic:\n"
	);

	FileContent.Append("\tFOceanologyGroupedWaterPresets();\n\npublic:\n");

	for (FString PresetGroupName : PresetGroupNames)
	{
		FileContent.Append("\tUPROPERTY(EditAnywhere, BlueprintReadWrite, Category=\"Preset\")\n");
		FileContent.Append("\tUOceanologyWater" + PresetGroupName + "Preset* " + PresetGroupName + " = nullptr;\n\n");
	}

	FileContent.Append("\npublic:\n");
	FileContent.Append(
		"\tstatic void LoadPresets(const FOceanologyGroupedWaterPresets& Presets, AOceanologyWaterParent* Water, const TArray<FName>& InclusionGroups);\n"
	);

	FileContent.Append(
		"\tstatic void ClearPropertyUpdatePresets(const FOceanologyGroupedWaterPresets& Presets, AOceanologyWaterParent* Water);\n"
	);

	FileContent.Append(
		"\tstatic void OnPropertyUpdatePresets(const FOceanologyGroupedWaterPresets& Presets, AOceanologyWaterParent* Water);\n"
	);

	FileContent.Append("\n");
	FileContent.Append("};\n\n");

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WritePrivateGroupedWaterPresetsStruct()
{
	const FString FilePath = StructsPrivateSourceCodeDirectory + "OceanologyGroupedWaterPresets.cpp";
	FString FileContent = "";

	FileContent.Append("#include \"Structs/OceanologyGroupedWaterPresets.h\"\n");
	
	for (FString PresetGroupName : PresetGroupNames)
	{
		FileContent.Append("#include \"DataAsset/OceanologyWater" + PresetGroupName + "Preset.h\"\n");
	}

	FileContent.Append("#include \"Actors/OceanologyWaterParentActor.h\"\n\n");


	AppendHeader(FileContent);

	FileContent.Append("FOceanologyGroupedWaterPresets::FOceanologyGroupedWaterPresets()\n");
	FileContent.Append("{\n");
	FileContent.Append("}\n\n");

	FileContent.Append(
		"void FOceanologyGroupedWaterPresets::LoadPresets(const FOceanologyGroupedWaterPresets& Presets, AOceanologyWaterParent* Water, const TArray<FName>& InclusionGroups)\n");
	FileContent.Append("{\n");
	for (FString PresetGroupName : PresetGroupNames)
	{
		FileContent.Append("\tif (Presets." + PresetGroupName + ")\n\t{\n");
		FileContent.Append("\t\tPresets.").Append(PresetGroupName).Append("->LoadPreset(Water, InclusionGroups);\n");
		FileContent.Append("\t}\n\n");
	}
	FileContent.Append("}\n\n");

	FileContent.Append(
	"void FOceanologyGroupedWaterPresets::ClearPropertyUpdatePresets(const FOceanologyGroupedWaterPresets& Presets, AOceanologyWaterParent* Water)\n");
	FileContent.Append("{\n");
	FileContent.Append("#if WITH_EDITOR\n");
	for (FString PresetGroupName : PresetGroupNames)
	{
		FileContent.Append("\tif (Presets." + PresetGroupName + ")\n\t{\n");
		FileContent.Append("\t\tPresets.").Append(PresetGroupName).Append("->OnPropertyUpdated.RemoveAll(Water);\n");
		FileContent.Append("\t}\n\n");
	}
	FileContent.Append("#endif\n");
	FileContent.Append("}\n\n");

	FileContent.Append(
		"void FOceanologyGroupedWaterPresets::OnPropertyUpdatePresets(const FOceanologyGroupedWaterPresets& Presets, AOceanologyWaterParent* Water)\n");
	FileContent.Append("{\n");
	FileContent.Append("#if WITH_EDITOR\n");
	for (FString PresetGroupName : PresetGroupNames)
	{
		FileContent.Append("\tif (Presets." + PresetGroupName + ")\n\t{\n");
		FileContent.Append("\t\tPresets.").Append(PresetGroupName).Append("->OnPropertyUpdated.RemoveAll(Water);\n");
		FileContent.Append("\t\tPresets.").Append(PresetGroupName).Append(
			"->OnPropertyUpdated.AddUObject(Water, &AOceanologyWaterParent::PostEditChange);\n");
		FileContent.Append("\t}\n\n");
	}
	FileContent.Append("#endif\n");
	FileContent.Append("}\n");

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WriteAllPublicGroupedWaterPresetDataAsset()
{
	for (FString PresetGroupName : PresetGroupNames)
	{
		WritePublicGroupedWaterPresetDataAsset(PresetGroupName);
	}
}

void AOceanologyPresetConverterActor::WriteAllPrivateGroupedWaterPresetDataAsset()
{
	for (FString PresetGroupName : PresetGroupNames)
	{
		WritePrivateGroupedWaterPresetDataAsset(PresetGroupName);
	}
}

void AOceanologyPresetConverterActor::WritePublicGroupedWaterPresetDataAsset(const FString& PresetGroup)
{
	const FString FilePath = PresetsPublicSourceCodeDirectory + "OceanologyWater" + PresetGroup + "Preset.h";
	FString FileContent = "";

	AppendCommonPublicPreset(PresetGroup, FileContent);

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WritePrivateGroupedWaterPresetDataAsset(const FString& PresetGroup)
{
	const FString FilePath = PresetsPrivateSourceCodeDirectory + "OceanologyWater" + PresetGroup + "Preset.cpp";
	FString FileContent = "";

	AppendCommonPrivatePreset(
		PresetGroup,
		FileContent,
		PresetGroup.Contains("Waves"),
		PresetGroup.Contains("Underwater")
	);

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WriteAllPublicPresetFactory()
{
	for (FString PresetGroupName : PresetGroupNames)
	{
		WritePublicPresetFactory(PresetGroupName);
	}
}

void AOceanologyPresetConverterActor::WriteAllPrivatePresetFactory()
{
	for (FString PresetGroupName : PresetGroupNames)
	{
		WritePrivatePresetFactory(PresetGroupName);
	}
}

void AOceanologyPresetConverterActor::WritePublicPresetFactory(const FString& PresetGroup)
{
	const FString FilePath = PresetFactoryPublicSourceCodeDirectory + "OceanologyWater" + PresetGroup +
		"PresetFactory.h";
	FString FileContent = "";

	FileContent.Append("#pragma once\n\n");
	FileContent.Append("#include \"CoreMinimal.h\"\n");
	FileContent.Append("#include \"Factories/Factory.h\"\n");
	FileContent.Append("#include \"OceanologyWater" + PresetGroup +
		"PresetFactory.generated.h\"\n");
	FileContent.Append("\n");

	AppendHeader(FileContent);
	FileContent.Append("UCLASS()\n");
	FileContent.Append("class OCEANOLOGY_PLUGINEDITOR_API UOceanologyWater" + PresetGroup +
		"PresetFactory : public UFactory\n");
	FileContent.Append("{\n");
	FileContent.Append("\tGENERATED_BODY()\n\n");
	FileContent.Append("public:\n")
	           .Append("\tUOceanologyWater" + PresetGroup + "PresetFactory();\n\n");

	FileContent.Append(
		"\tvirtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;\n");
	FileContent.Append("\tvirtual FText GetDisplayName() const override;\n");
	FileContent.Append("\tvirtual FString GetDefaultNewAssetName() const override;\n");
	FileContent.Append("};\n");

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::WritePrivatePresetFactory(const FString& PresetGroup)
{
	const FString FilePath = PresetFactoryPrivateSourceCodeDirectory + "OceanologyWater" + PresetGroup +
		"PresetFactory.cpp";
	FString FileContent = "";

	FileContent.Append("#include \"DataAsset/OceanologyWater" + PresetGroup + "PresetFactory.h\"\n");
	FileContent.Append("#include \"DataAsset/OceanologyWater" + PresetGroup + "Preset.h\"\n");
	FileContent.Append("\n");
	FileContent.Append("#define LOCTEXT_NAMESPACE \"OceanologyEditor\"\n\n");
	AppendHeader(FileContent);
	FileContent.Append("UOceanologyWater" + PresetGroup + "PresetFactory::UOceanologyWater" + PresetGroup +
		"PresetFactory()\n");
	FileContent.Append("{\n");
	FileContent.Append("\tbCreateNew = true;\n");
	FileContent.Append("\tbEditAfterNew = true;\n");
	FileContent.Append("\tSupportedClass = UOceanologyWater" + PresetGroup +
		"Preset::StaticClass();\n");
	FileContent.Append("}\n\n");
	FileContent.Append("UObject* UOceanologyWater" + PresetGroup +
		"PresetFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)\n");
	FileContent.Append("{\n");
	FileContent.Append(
		"\tUOceanologyWater" + PresetGroup + "Preset* OceanologyPreset = NewObject<UOceanologyWater" + PresetGroup +
		"Preset>(InParent, Class, Name, Flags);\n");
	FileContent.Append("\treturn OceanologyPreset;\n");
	FileContent.Append("}\n\n");
	FileContent.Append("FText UOceanologyWater" + PresetGroup + "PresetFactory::GetDisplayName() const\n");
	FileContent.Append("{\n");
	FileContent.Append(
		"\treturn LOCTEXT(\"OceanologyWater" + PresetGroup + "PresetText\", \"Oceanology Water " + PresetGroup +
		" Preset\");\n");
	FileContent.Append("}\n\n");
	FileContent.Append("FString UOceanologyWater" + PresetGroup + "PresetFactory::GetDefaultNewAssetName() const\n");
	FileContent.Append("{\n");
	FileContent.Append("\treturn FString(TEXT(\"NewWater" + PresetGroup + "Preset\"));");
	FileContent.Append("}\n\n");

	WriteOutputFile(FilePath, FileContent);
}

void AOceanologyPresetConverterActor::AppendHeader(FString& FileContent)
{
	FileContent.Append(
		"/**\n * This is an auto-generated class from WaterParent actor via AOceanologyPresetConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit WaterParent structs instead!\n * Generated at: "
		+ FText::AsDate(FDateTime::Now(), EDateTimeStyle::Long).ToString() + "\n **/\n"
	);
}

void AOceanologyPresetConverterActor::AppendProperty(FString& FileContent, FOceanologyPresetGroup PresetGroup)
{
	FileContent.Append("\tUPROPERTY(EditAnywhere, BlueprintReadWrite, Category=\"")
	           .Append(PresetGroup.PresetGroup).Append("\"")
	           .Append(PresetGroup.EditCondition != ""
		                   ? (", meta=(EditCondition=\"" + PresetGroup.EditCondition + "\")")
		                   : "")
	           .Append(")\n");
	FileContent.Append("\t" + PresetGroup.PropertyType + " ").Append(PresetGroup.PropertyName)
	           .Append(PresetGroup.PropertyType == "bool" ? " = false" : "")
	           .Append(PresetGroup.DefaultValue != "None" ? " = " + PresetGroup.DefaultValue : "")
	           .Append(";\n\n");
}

void AOceanologyPresetConverterActor::AppendCommonPublicPreset(const FString& PresetGroup, FString& FileContent)
{
	FileContent.Append("#pragma once\n\n");
	FileContent.Append("#include \"CoreMinimal.h\"\n");
	FileContent.Append("#include \"Engine/DataAsset.h\"\n");

	for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
	{
		if (LoopPresetGroup.TypeClass.IsEmpty() || (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup))
		{
			continue;
		}

		FileContent.Append("#include \"").Append(LoopPresetGroup.TypeClass).Append(".h\"\n");
	}

	FileContent.Append("#include \"Structs/OceanologyWaterPresetResult.h\"\n");
	FileContent.Append("#include \"OceanologyWater" + PresetGroup + "Preset.generated.h\"\n");
	FileContent.Append("\n\n");


	FileContent.Append("class AOceanologyWaterParent;\n\n");

	FileContent.Append("UCLASS(BlueprintType)\n").Append(
		"class OCEANOLOGY_PLUGIN_API UOceanologyWater" + PresetGroup +
		"Preset : public UDataAsset\n{\n\tGENERATED_BODY()\n\n");

	AppendHeader(FileContent);
	FileContent.Append("public:\n\t");
	FileContent.Append("UOceanologyWater" + PresetGroup + "Preset();\n\n");
	FileContent.Append("#if WITH_EDITOR\n\tFSimpleMulticastDelegate OnPropertyUpdated;\n#endif\n\n");

	for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
	{
		if (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup)
		{
			continue;
		}

		AppendProperty(FileContent, LoopPresetGroup);
	}

	FileContent.Append("\n");
	FileContent.Append("public:\n");
	FileContent.Append(
		"\tvirtual void LoadPreset(AOceanologyWaterParent* Water, const TArray<FName> InclusionGroups) const;\n\n");

	if (PresetGroup != "")
	{
		FileContent.Append("\tUFUNCTION(BlueprintPure, Category=\"Water " + PresetGroup + " Preset\")\n");
	}
	FileContent.Append("\tstatic void LerpPreset(\n")
	           .Append("\t\tconst UOceanologyWater" + PresetGroup + "Preset* A,\n")
	           .Append("\t\tconst UOceanologyWater" + PresetGroup + "Preset* B,\n")
	           .Append("\t\tconst double Alpha,\n")
	           .Append("\t\tFOceanologyWaterPresetResult& OutPresetResult\n")
	           .Append("\t);\n\n");

	FileContent.Append(
		"#if WITH_EDITOR\n\tvirtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;\n#endif\n"
	);

	FileContent.Append("\n};\n");
}

void AOceanologyPresetConverterActor::AppendCommonPrivatePreset(
	const FString& PresetGroup,
	FString& FileContent,
	bool UseWaves,
	bool UseUnderwater
)
{
	FileContent.Append("#include \"DataAsset/OceanologyWater" + PresetGroup + "Preset.h\"\n");
	FileContent.Append("#include \"Actors/OceanologyWaterParentActor.h\"\n");
	if (UseUnderwater)
	{
		FileContent.Append("#include \"Components/OceanologyUnderwaterComponent.h\"\n");
	}

	if (UseWaves)
	{
		FileContent.Append("#include \"Components/Wave/OceanologyWaveSolverComponent.h\"\n");
		FileContent.Append("#include \"Components/Wave/OceanologyGerstnerWaveSolverComponent.h\"\n");
	}

	FileContent.Append("\n");

	AppendHeader(FileContent);
	FileContent.Append(
		"UOceanologyWater" + PresetGroup + "Preset::UOceanologyWater" + PresetGroup + "Preset()\n{\n}\n\n");

	{
		FileContent.Append("void UOceanologyWater" + PresetGroup + "Preset::LoadPreset(\n")
		           .Append("\tAOceanologyWaterParent* Water,\n")
		           .Append("\tconst TArray<FName> InclusionGroups\n")
		           .Append(") const\n{\n");

		if (UseWaves)
		{
			FileContent.Append("\tif (UOceanologyWaveSolverComponent* WaveSolver = Water->GetWaveSolver())\n");
			FileContent.Append("\t{\n");

			for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
			{
				if (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup)
				{
					continue;
				}

				if (LoopPresetGroup.PresetClassSource == "WaveSolver")
				{
					FileContent.Append("\t\tif (InclusionGroups.Contains(\"" + LoopPresetGroup.PropertyName + "\"))\n");
					FileContent.Append("\t\t{\n");
					FileContent.Append(
						"\t\t\tWaveSolver->" + LoopPresetGroup.PropertyName + " = " + LoopPresetGroup.PropertyName +
						";\n"
					);
					FileContent.Append("\t\t}\n");
				}
			}

			{
				FileContent.Append("\t\tif (WaveSolver->IsA(UOceanologyGerstnerWaveSolverComponent::StaticClass()))\n");
				FileContent.Append("\t\t{\n");
				FileContent.Append(
					           "\t\t\tUOceanologyGerstnerWaveSolverComponent* GerstnerWaveSolver = Cast<UOceanologyGerstnerWaveSolverComponent>(\n"
				           )
				           .Append("\t\t\t\tWaveSolver\n")
				           .Append("\t\t\t);\n\n");

				for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
				{
					if (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup)
					{
						continue;
					}

					if (LoopPresetGroup.PresetClassSource == "GerstnerWaveSolver")
					{
						FileContent.Append(
							"\t\t\tif (InclusionGroups.Contains(\"" + LoopPresetGroup.PropertyName + "\"))\n");
						FileContent.Append("\t\t\t{\n");
						FileContent.Append(
							"\t\t\t\tGerstnerWaveSolver->" + LoopPresetGroup.PropertyName + " = " + LoopPresetGroup.
							PropertyName
							+ ";\n"
						);
						FileContent.Append("\t\t\t}\n");
					}
				}

				FileContent.Append("\t\t}\n");
			}

			FileContent.Append("\t}\n\n");
		}

		FileContent.Append("\n\n");

		for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
		{
			if (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup)
			{
				continue;
			}

			if (LoopPresetGroup.PresetClassSource == "WaterParent")
			{
				FileContent.Append("\tif (InclusionGroups.Contains(\"" + LoopPresetGroup.PropertyName + "\"))\n");
				FileContent.Append("\t{\n");
				FileContent.Append(
					"\t\tWater->" + LoopPresetGroup.PropertyName + " = " + LoopPresetGroup.PropertyName + ";\n"
				);
				FileContent.Append("\t}\n");
			}
		}

		FileContent.Append("\n\n");

		if (UseUnderwater)
		{
			FileContent.Append("\tif (Water->UnderwaterComponent)\n")
			           .Append("\t{\n");

			for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
			{
				if (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup)
				{
					continue;
				}

				if (LoopPresetGroup.PresetClassSource == "Underwater")
				{
					FileContent.Append("\t\tif (InclusionGroups.Contains(\"" + LoopPresetGroup.PropertyName + "\"))\n");
					FileContent.Append("\t\t{\n");
					FileContent.Append(
						"\t\t\tWater->UnderwaterComponent->" + LoopPresetGroup.PropertyName + " = " + LoopPresetGroup.
						PropertyName
						+
						";\n"
					);
					FileContent.Append("\t\t}\n");
				}
			}

			FileContent.Append("\t}\n");
		}

		FileContent.Append("}\n\n");
	}

	FileContent.Append("");

	FileContent.Append("void UOceanologyWater" + PresetGroup + "Preset::LerpPreset(\n");
	FileContent.Append("\tconst UOceanologyWater" + PresetGroup + "Preset* A,\n");
	FileContent.Append("\tconst UOceanologyWater" + PresetGroup + "Preset* B,\n");
	FileContent.Append("\tconst double Alpha,\n");
	FileContent.Append("\tFOceanologyWaterPresetResult& OutPresetResult\n");
	FileContent.Append(")\n");
	FileContent.Append("{\n");
	FileContent.Append("\tFOceanologyWaterPresetResult LocalWaterPresetResult;\n\n");
	FileContent.Append("\t//@formatter:off\n");
	for (FOceanologyPresetGroup LoopPresetGroup : PresetGroups)
	{
		if (PresetGroup != "" && LoopPresetGroup.PresetGroup != PresetGroup)
		{
			continue;
		}

		if (LoopPresetGroup.PresetNoLerp == "true")
		{
			continue;
		}

		if (LoopPresetGroup.PropertyType == "bool" || LoopPresetGroup.TypeClass.Contains("Enums/"))
		{
			FileContent.Append("\t").Append("LocalWaterPresetResult.").Append(LoopPresetGroup.PropertyName)
			           .Append(" = B->").Append(LoopPresetGroup.PropertyName).Append(";\n");
		}
		else
		{
			FileContent.Append("\tUOceanology").Append(LoopPresetGroup.PropertyName).Append("Helper::Lerp")
			           .Append(LoopPresetGroup.PropertyName)
			           .Append("(")
			           .Append("A->" + LoopPresetGroup.PropertyName + ", ").Append(
				           "B->" + LoopPresetGroup.PropertyName + ", ")
			           .Append("Alpha, LocalWaterPresetResult.").Append(LoopPresetGroup.PropertyName)
			           .Append(");\n");
		}
	}
	FileContent.Append("\t//@formatter:on\n\n");
	FileContent.Append("\tOutPresetResult = LocalWaterPresetResult;\n");
	FileContent.Append("}\n\n");


	FileContent.Append("#if WITH_EDITOR\n");
	FileContent.Append(
		"void UOceanologyWater" + PresetGroup +
		"Preset::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)\n");
	FileContent.Append("{\n");
	FileContent.Append("\tSuper::PostEditChangeProperty(PropertyChangedEvent);\n\n");
	FileContent.Append("\tOnPropertyUpdated.Broadcast();\n");
	FileContent.Append("}\n");
	FileContent.Append("#endif\n");
}

void AOceanologyPresetConverterActor::WriteOutputFile(const FString& FilePath, const FString& FileContent) const
{
	if (FPaths::ValidatePath(FilePath) && FPaths::FileExists(FilePath))
	{
		if (IFileManager& FileManager = IFileManager::Get(); FileManager.Delete(*FilePath))
		{
			UKismetSystemLibrary::PrintString(
				this,
				"Cleaned file: " + FilePath,
				true,
				true,
				FLinearColor::Green,
				1.0
			);
		}
	}

	if (FFileHelper::SaveStringToFile(
		FileContent,
		*FilePath,
		FFileHelper::EEncodingOptions::AutoDetect,
		&IFileManager::Get(),
		FILEWRITE_Append
	))
	{
		UKismetSystemLibrary::PrintString(
			this,
			"Generated file: " + FilePath,
			true,
			true,
			FLinearColor::Green,
			1.0
		);
	}
	else
	{
		UKismetSystemLibrary::PrintString(
			this,
			"Failed to generated file: " + FilePath,
			true,
			true,
			FLinearColor::Red,
			1.0
		);
	}
}

void AOceanologyPresetConverterActor::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);

	if (!Water)
	{
		Water = Cast<AOceanologyWaterParent>(
			UGameplayStatics::GetActorOfClass(this, AOceanologyInfiniteOcean::StaticClass())
		);
		PostEditChange();
		Modify();
	}
}
