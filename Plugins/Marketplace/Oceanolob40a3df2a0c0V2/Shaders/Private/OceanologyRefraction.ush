#pragma once

float Refraction(
	in float WaterRefractionNear,
	in float WaterRefractionFar,
	in float3 PixelNormalWS,
	in float3 CameraPosition,
	in float3 AbsoluteWorldPosition
)
{
	float DistanceResult = distance(AbsoluteWorldPosition, CameraPosition) / 5000.0;
	float SaturateResult = saturate(DistanceResult);
	float LerpResult = lerp(WaterRefractionNear, WaterRefractionFar, SaturateResult);
	float SaturateResultB = saturate(PixelNormalWS.b + 0.1);

	return lerp(1.0, LerpResult, SaturateResultB);
}
