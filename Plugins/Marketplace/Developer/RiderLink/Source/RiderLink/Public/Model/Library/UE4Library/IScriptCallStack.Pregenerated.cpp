//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a RdGen v1.13.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "IScriptCallStack.Pregenerated.h"


#include "IScriptCallStack_Unknown.Pregenerated.h"

#ifdef _MSC_VER
#pragma warning( push )
#pragma warning( disable:4250 )
#pragma warning( disable:4307 )
#pragma warning( disable:4267 )
#pragma warning( disable:4244 )
#pragma warning( disable:4100 )
#endif

namespace JetBrains {
namespace EditorPlugin {
// companion
// constants
constexpr rd::wstring_view IScriptCallStack::header;
// initializer
void IScriptCallStack::initialize()
{
}
// primary ctor
// secondary constructor
// default ctors and dtors
IScriptCallStack::IScriptCallStack()
{
    initialize();
}
// reader
rd::Wrapper<IScriptCallStack> IScriptCallStack::readUnknownInstance(rd::SerializationCtx& ctx, rd::Buffer & buffer, rd::RdId const& unknownId, int32_t size)
{
    int32_t objectStartPosition = buffer.get_position();
    auto unknownBytes = rd::Buffer::ByteArray(objectStartPosition + size - buffer.get_position());
    buffer.read_byte_array_raw(unknownBytes);
    IScriptCallStack_Unknown res{unknownId, unknownBytes};
    return rd::Wrapper<IScriptCallStack_Unknown>(std::move(res));
}
// writer
// virtual init
// identify
// getters
// intern
// equals trait
// equality operators
bool operator==(const IScriptCallStack &lhs, const IScriptCallStack &rhs) {
    if (lhs.type_name() != rhs.type_name()) return false;
    return lhs.equals(rhs);
}
bool operator!=(const IScriptCallStack &lhs, const IScriptCallStack &rhs){
    return !(lhs == rhs);
}
// hash code trait
size_t IScriptCallStack::hashCode() const noexcept
{
    size_t __r = 0;
    return __r;
}
// type name trait
std::string IScriptCallStack::type_name() const
{
    return "IScriptCallStack";
}
// static type name trait
std::string IScriptCallStack::static_type_name()
{
    return "IScriptCallStack";
}
// polymorphic to string
std::string IScriptCallStack::toString() const
{
    std::string res = "IScriptCallStack\n";
    return res;
}
// external to string
std::string to_string(const IScriptCallStack & value)
{
    return value.toString();
}
}
}

#ifdef _MSC_VER
#pragma warning( pop )
#endif

