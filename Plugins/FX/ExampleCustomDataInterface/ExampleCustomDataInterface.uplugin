{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Niagara Example Custom DataInterface", "Description": "This plugin contains C++ example content that shows how to write your own data interface for Niagara. \nCheck out the plugin folder Engine/Plugins/FX/ExampleCustomDataInterface for the source files.", "Category": "Other", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "ExampleCustomDataInterface", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "Niagara", "Enabled": true}]}