# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "kilogramsPerUnit": {
                        "appliesTo": [
                            "layers"
                        ], 
                        "default": 1.0, 
                        "displayGroup": "Stage", 
                        "type": "double"
                    }
                }, 
                "Types": {
                    "UsdPhysicsArticulationRootAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsArticulationRootAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsCollisionAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsCollisionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsCollisionGroup": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsCollisionGroup"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsDistanceJoint": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsDistanceJoint"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdPhysicsJoint"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsDriveAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsDriveAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "multipleApplyAPI"
                    }, 
                    "UsdPhysicsFilteredPairsAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsFilteredPairsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsFixedJoint": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsFixedJoint"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdPhysicsJoint"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsJoint": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsJoint"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomImageable"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsLimitAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsLimitAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "multipleApplyAPI"
                    }, 
                    "UsdPhysicsMassAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsMassAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsMaterialAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsMaterialAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsMeshCollisionAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsMeshCollisionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsPrismaticJoint": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsPrismaticJoint"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdPhysicsJoint"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsRevoluteJoint": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsRevoluteJoint"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdPhysicsJoint"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsRigidBodyAPI": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsRigidBodyAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdPhysicsScene": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsScene"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdPhysicsSphericalJoint": {
                        "alias": {
                            "UsdSchemaBase": "PhysicsSphericalJoint"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdPhysicsJoint"
                        ], 
                        "schemaKind": "concreteTyped"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdPhysics.dylib", 
            "Name": "usdPhysics", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
