# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdHydraDiscoveryPlugin": {
                        "bases": [
                            "NdrDiscoveryPlugin"
                        ], 
                        "displayName": "Discovery plugin for deprecated hydra shaders."
                    }, 
                    "UsdHydraGenerativeProceduralAPI": {
                        "alias": {
                            "UsdSchemaBase": "HydraGenerativeProceduralAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GenerativeProcedural"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "LibraryPath": "../../../../../../../../Binaries/Win64/usd_usdHydra.dll", 
            "Name": "usdHydra", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
