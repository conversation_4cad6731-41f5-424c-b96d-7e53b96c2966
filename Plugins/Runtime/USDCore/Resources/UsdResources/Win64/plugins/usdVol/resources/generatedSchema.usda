#usda 1.0
(
    "WARNING: THIS FILE IS GENERATED BY usdGenSchema.  DO NOT EDIT."
)

class Volume "Volume" (
    doc = """A renderable volume primitive. A volume is made up of any number
             of FieldBase primitives bound together in this volume. Each
             FieldBase primitive is specified as a relationship with a
             namespace prefix of \"field\".

             The relationship name is used by the renderer to associate
             individual fields with the named input parameters on the volume
             shader. Using this indirect approach to connecting fields to
             shader parameters (rather than using the field prim's name)
             allows a single field to be reused for different shader inputs, or
             to be used as different shader parameters when rendering different
             Volumes. This means that the name of the field prim is not
             relevant to its contribution to the volume prims which refer to
             it. Nor does the field prim's location in the scene graph have
             any relevance, and Volumes may refer to fields anywhere in the
             scene graph.  **However**, unless Field prims need to be shared
             by multiple Volumes, a Volume's Field prims should be located
             under the Volume in namespace, for enhanced organization."""
)
{
    uniform bool doubleSided = 0 (
        doc = """Although some renderers treat all parametric or polygonal
        surfaces as if they were effectively laminae with outward-facing
        normals on both sides, some renderers derive significant optimizations
        by considering these surfaces to have only a single outward side,
        typically determined by control-point winding order and/or 
        orientation.  By doing so they can perform \"backface culling\" to
        avoid drawing the many polygons of most closed surfaces that face away
        from the viewer.
        
        However, it is often advantageous to model thin objects such as paper
        and cloth as single, open surfaces that must be viewable from both
        sides, always.  Setting a gprim's doubleSided attribute to 
        \\c true instructs all renderers to disable optimizations such as
        backface culling for the gprim, and attempt (not all renderers are able
        to do so, but the USD reference GL renderer always will) to provide
        forward-facing normals on each side of the surface for lighting
        calculations."""
    )
    float3[] extent (
        doc = """Extent is a three dimensional range measuring the geometric
        extent of the authored gprim in its own local space (i.e. its own
        transform not applied), without accounting for any shader-induced
        displacement. If __any__ extent value has been authored for a given 
        Boundable, then it should be authored at every timeSample at which 
        geometry-affecting properties are authored, to ensure correct 
        evaluation via ComputeExtent(). If __no__ extent value has been 
        authored, then ComputeExtent() will call the Boundable's registered 
        ComputeExtentFunction(), which may be expensive, which is why we 
        strongly encourage proper authoring of extent.
        \\sa ComputeExtent()
        \\sa \\ref UsdGeom_Boundable_Extent.
        
        An authored extent on a prim which has children is expected to include
        the extent of all children, as they will be pruned from BBox computation
        during traversal."""
    )
    uniform token orientation = "rightHanded" (
        allowedTokens = ["rightHanded", "leftHanded"]
        doc = """Orientation specifies whether the gprim's surface normal 
        should be computed using the right hand rule, or the left hand rule.
        Please see for a deeper explanation and
        generalization of orientation to composed scenes with transformation
        hierarchies."""
    )
    color3f[] primvars:displayColor (
        doc = '''It is useful to have an "official" colorSet that can be used
        as a display or modeling color, even in the absence of any specified
        shader for a gprim.  DisplayColor serves this role; because it is a
        UsdGeomPrimvar, it can also be used as a gprim override for any shader
        that consumes a displayColor parameter.'''
    )
    float[] primvars:displayOpacity (
        doc = """Companion to displayColor that specifies opacity, broken
        out as an independent attribute rather than an rgba color, both so that
        each can be independently overridden, and because shaders rarely consume
        rgba parameters."""
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class "FieldBase" (
    doc = "Base class for field primitives."
)
{
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class "FieldAsset" (
    doc = "Base class for field primitives defined by an external file."
)
{
    token fieldDataType (
        doc = """Token which is used to indicate the data type of an
                 individual field. Authors use this to tell consumers more
                 about the field without opening the file on disk. The list of 
                 allowed tokens is specified with the specific asset type. 
                 A missing value is considered an error."""
    )
    int fieldIndex (
        doc = """A file can contain multiple fields with the same
                 name. This optional attribute is an index used to
                 disambiguate between these multiple fields with the same
                 name."""
    )
    token fieldName (
        doc = """Name of an individual field within the file specified by
                 the filePath attribute."""
    )
    asset filePath (
        doc = """An asset path attribute that points to a file on disk.
                 For each supported file format, a separate FieldAsset
                 subclass is required. 
                  
                 This attribute's value can be animated over time, as most
                 volume asset formats represent just a single timeSample of
                 a volume.  However, it does not, at this time, support
                 any pattern substitutions like \"$F\". """
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    token vectorDataRoleHint = "None" (
        allowedTokens = ["None", "Point", "Normal", "Vector", "Color"]
        doc = """Optional token which is used to indicate the role of a vector
                 valued field. This can drive the data type in which fields
                 are made available in a renderer or whether the vector values 
                 are to be transformed."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class Field3DAsset "Field3DAsset" (
    doc = """Field3D field primitive. The FieldAsset filePath attribute must
             specify a file in the Field3D format on disk."""
)
{
    token fieldDataType (
        allowedTokens = ["half", "float", "double", "half3", "float3", "double3"]
        doc = """Token which is used to indicate the data type of an
                 individual field. Authors use this to tell consumers more
                 about the field without opening the file on disk. The list of 
                 allowed tokens reflects the available choices for Field3d 
                 volumes."""
    )
    int fieldIndex (
        doc = """A file can contain multiple fields with the same
                 name. This optional attribute is an index used to
                 disambiguate between these multiple fields with the same
                 name."""
    )
    token fieldName (
        doc = """Name of an individual field within the file specified by
                 the filePath attribute."""
    )
    token fieldPurpose (
        doc = """Optional token which can be used to indicate the purpose or 
                 grouping of an individual field. Clients which consume Field3D 
                 files should treat this as the Field3D field name."""
    )
    asset filePath (
        doc = """An asset path attribute that points to a file on disk.
                 For each supported file format, a separate FieldAsset
                 subclass is required. 
                  
                 This attribute's value can be animated over time, as most
                 volume asset formats represent just a single timeSample of
                 a volume.  However, it does not, at this time, support
                 any pattern substitutions like \"$F\". """
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    token vectorDataRoleHint = "None" (
        allowedTokens = ["None", "Point", "Normal", "Vector", "Color"]
        doc = """Optional token which is used to indicate the role of a vector
                 valued field. This can drive the data type in which fields
                 are made available in a renderer or whether the vector values 
                 are to be transformed."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class OpenVDBAsset "OpenVDBAsset" (
    doc = """OpenVDB field primitive. The FieldAsset filePath attribute must
             specify a file in the OpenVDB format on disk."""
)
{
    token fieldClass (
        allowedTokens = ["levelSet", "fogVolume", "staggered", "unknown"]
        doc = """Optional token which can be used to indicate the class of
                 an individual grid. This is a mapping to openvdb::GridClass
                 where the values are GRID_LEVEL_SET, GRID_FOG_VOLUME, 
                 GRID_STAGGERED, and GRID_UNKNOWN."""
    )
    token fieldDataType (
        allowedTokens = ["half", "float", "double", "int", "uint", "int64", "half2", "float2", "double2", "int2", "half3", "float3", "double3", "int3", "matrix3d", "matrix4d", "quatd", "bool", "mask", "string"]
        doc = """Token which is used to indicate the data type of an
                 individual field. Authors use this to tell consumers more
                 about the field without opening the file on disk. The list of 
                 allowed tokens reflects the available choices for OpenVDB 
                 volumes."""
    )
    int fieldIndex (
        doc = """A file can contain multiple fields with the same
                 name. This optional attribute is an index used to
                 disambiguate between these multiple fields with the same
                 name."""
    )
    token fieldName (
        doc = """Name of an individual field within the file specified by
                 the filePath attribute."""
    )
    asset filePath (
        doc = """An asset path attribute that points to a file on disk.
                 For each supported file format, a separate FieldAsset
                 subclass is required. 
                  
                 This attribute's value can be animated over time, as most
                 volume asset formats represent just a single timeSample of
                 a volume.  However, it does not, at this time, support
                 any pattern substitutions like \"$F\". """
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    token vectorDataRoleHint = "None" (
        allowedTokens = ["None", "Point", "Normal", "Vector", "Color"]
        doc = """Optional token which is used to indicate the role of a vector
                 valued field. This can drive the data type in which fields
                 are made available in a renderer or whether the vector values 
                 are to be transformed."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

