// <PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, Redmond WA 98052
// Copyright (c) 1998-2019
// Distributed under the Boost Software License, Version 1.0.
// http://www.boost.org/LICENSE_1_0.txt
// http://www.geometrictools.com/License/Boost/LICENSE_1_0.txt
// File Version: 3.0.29 (2019/02/13)

#pragma once

// I used to have these sections ordered alphabetically by the names in
// the comments.  Two phase name lookups for template matching by the
// MSVS 2017 compiler had no problems with the ordering, but the Linux
// g++ compiler does.  This occurred when trying to match std::sqrt(...)
// and other math functions when the inputs are based on BSNumber or
// BSRational.  The "Arithmetic" section has been moved before all other
// headers, and the UInteger* files have been moved before the BS* files.

// Arithmetic
#include <ThirdParty/GTEngine/Mathematics/GteBitHacks.h>
#include <ThirdParty/GTEngine/Mathematics/GteIEEEBinary.h>
#include <ThirdParty/GTEngine/Mathematics/GteIEEEBinary16.h>
#include <ThirdParty/GTEngine/Mathematics/GteMath.h>
#include <ThirdParty/GTEngine/Mathematics/GteArbitraryPrecision.h>
#include <ThirdParty/GTEngine/Mathematics/GteQuadraticField.h>

// Algebra
#include <ThirdParty/GTEngine/Mathematics/GteAxisAngle.h>
#include <ThirdParty/GTEngine/Mathematics/GteBandedMatrix.h>
#include <ThirdParty/GTEngine/Mathematics/GteConvertCoordinates.h>
#include <ThirdParty/GTEngine/Mathematics/GteEulerAngles.h>
#include <ThirdParty/GTEngine/Mathematics/GteGMatrix.h>
#include <ThirdParty/GTEngine/Mathematics/GteGVector.h>
#include <ThirdParty/GTEngine/Mathematics/GteMatrix.h>
#include <ThirdParty/GTEngine/Mathematics/GteMatrix2x2.h>
#include <ThirdParty/GTEngine/Mathematics/GteMatrix3x3.h>
#include <ThirdParty/GTEngine/Mathematics/GteMatrix4x4.h>
#include <ThirdParty/GTEngine/Mathematics/GtePolynomial1.h>
#include <ThirdParty/GTEngine/Mathematics/GteQuaternion.h>
#include <ThirdParty/GTEngine/Mathematics/GteRotation.h>
#include <ThirdParty/GTEngine/Mathematics/GteVector.h>
#include <ThirdParty/GTEngine/Mathematics/GteVector2.h>
#include <ThirdParty/GTEngine/Mathematics/GteVector3.h>
#include <ThirdParty/GTEngine/Mathematics/GteVector4.h>

// Approximation
#include <ThirdParty/GTEngine/Mathematics/GteApprCircle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprCone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprCylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprEllipseByArcs.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprEllipse2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprEllipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprGaussian2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprGaussian3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprGreatCircle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprHeightLine2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprHeightPlane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprOrthogonalLine2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprOrthogonalLine3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprOrthogonalPlane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprParaboloid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprPolynomial2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprPolynomial3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprPolynomial4.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprPolynomialSpecial2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprPolynomialSpecial3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprPolynomialSpecial4.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprQuadratic2.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprQuadratic3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprQuery.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprSphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteApprTorus3.h>

// ComputationalGeometry
#include <ThirdParty/GTEngine/Mathematics/GteConstrainedDelaunay2.h>
#include <ThirdParty/GTEngine/Mathematics/GteConvexHull2.h>
#include <ThirdParty/GTEngine/Mathematics/GteConvexHull3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDelaunay2.h>
#include <ThirdParty/GTEngine/Mathematics/GteDelaunay2Mesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteDelaunay3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDelaunay3Mesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteEdgeKey.h>
#include <ThirdParty/GTEngine/Mathematics/GteETManifoldMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteETNonmanifoldMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteFeatureKey.h>
#include <ThirdParty/GTEngine/Mathematics/GteIsPlanarGraph.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimalCycleBasis.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimumAreaBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimumAreaCircle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimumVolumeBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimumVolumeSphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteNearestNeighborQuery.h>
#include <ThirdParty/GTEngine/Mathematics/GteOBBTreeOfPoints.h>
#include <ThirdParty/GTEngine/Mathematics/GtePlanarMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GtePrimalQuery2.h>
#include <ThirdParty/GTEngine/Mathematics/GtePrimalQuery3.h>
#include <ThirdParty/GTEngine/Mathematics/GteSeparatePoints2.h>
#include <ThirdParty/GTEngine/Mathematics/GteSeparatePoints3.h>
#include <ThirdParty/GTEngine/Mathematics/GteTetrahedronKey.h>
#include <ThirdParty/GTEngine/Mathematics/GteTriangleKey.h>
#include <ThirdParty/GTEngine/Mathematics/GteTriangulateCDT.h>
#include <ThirdParty/GTEngine/Mathematics/GteTriangulateEC.h>
#include <ThirdParty/GTEngine/Mathematics/GteTSManifoldMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteUniqueVerticesTriangles.h>
#include <ThirdParty/GTEngine/Mathematics/GteVEManifoldMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteVETManifoldMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteVETNonmanifoldMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteVertexCollapseMesh.h>

// Containment
#include <ThirdParty/GTEngine/Mathematics/GteContAlignedBox.h>
#include <ThirdParty/GTEngine/Mathematics/GteContCapsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteContCircle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteContCone.h>
#include <ThirdParty/GTEngine/Mathematics/GteContCylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteContEllipse2.h>
#include <ThirdParty/GTEngine/Mathematics/GteContEllipse2MinCR.h>
#include <ThirdParty/GTEngine/Mathematics/GteContEllipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteContEllipsoid3MinCR.h>
#include <ThirdParty/GTEngine/Mathematics/GteContOrientedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteContOrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteContPointInPolygon2.h>
#include <ThirdParty/GTEngine/Mathematics/GteContPointInPolyhedron3.h>
#include <ThirdParty/GTEngine/Mathematics/GteContScribeCircle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteContScribeCircle3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteContSphere3.h>

// CurvesSurfacesVolumes
#include <ThirdParty/GTEngine/Mathematics/GteBasisFunction.h>
#include <ThirdParty/GTEngine/Mathematics/GteBezierCurve.h>
#include <ThirdParty/GTEngine/Mathematics/GteBSplineCurve.h>
#include <ThirdParty/GTEngine/Mathematics/GteBSplineCurveFit.h>
#include <ThirdParty/GTEngine/Mathematics/GteBSplineSurface.h>
#include <ThirdParty/GTEngine/Mathematics/GteBSplineSurfaceFit.h>
#include <ThirdParty/GTEngine/Mathematics/GteBSplineVolume.h>
#include <ThirdParty/GTEngine/Mathematics/GteDarbouxFrame.h>
#include <ThirdParty/GTEngine/Mathematics/GteFrenetFrame.h>
#include <ThirdParty/GTEngine/Mathematics/GteIndexAttribute.h>
#include <ThirdParty/GTEngine/Mathematics/GteMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteNaturalSplineCurve.h>
#include <ThirdParty/GTEngine/Mathematics/GteNURBSCircle.h>
#include <ThirdParty/GTEngine/Mathematics/GteNURBSCurve.h>
#include <ThirdParty/GTEngine/Mathematics/GteNURBSSurface.h>
#include <ThirdParty/GTEngine/Mathematics/GteNURBSSphere.h>
#include <ThirdParty/GTEngine/Mathematics/GteNURBSVolume.h>
#include <ThirdParty/GTEngine/Mathematics/GteParametricCurve.h>
#include <ThirdParty/GTEngine/Mathematics/GteParametricSurface.h>
#include <ThirdParty/GTEngine/Mathematics/GteRectangleMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteRectanglePatchMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteRevolutionMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteTCBSplineCurve.h>
#include <ThirdParty/GTEngine/Mathematics/GteTubeMesh.h>
#include <ThirdParty/GTEngine/Mathematics/GteVertexAttribute.h>

// Distance
#include <ThirdParty/GTEngine/Mathematics/GteDCPQuery.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistAlignedBoxAlignedBox.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistAlignedBox3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistCircle3Circle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLine3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLine3Circle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLine3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLine3Rectangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLine3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLineLine.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLineRay.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistLineSegment.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistOrientedBox3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3Circle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3ConvexPolyhedron3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3Frustum3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3Plane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3Rectangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPoint3Tetrahedron3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointAlignedBox.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointHyperellipsoid.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointLine.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointOrientedBox.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointRay.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointSegment.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointTriangle.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistPointTriangleExact.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRay3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRay3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRay3Rectangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRay3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRayRay.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRaySegment.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRectangle3Rectangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRectangle3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistRectangle3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistSegment3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistSegment3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistSegment3Rectangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistSegment3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistSegmentSegment.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistSegmentSegmentExact.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistTriangle3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistTriangle3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistTriangle3Rectangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteDistTriangle3Triangle3.h>

// Functions
#include <ThirdParty/GTEngine/Mathematics/GteACosEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteASinEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteATanEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteChebyshevRatio.h>
#include <ThirdParty/GTEngine/Mathematics/GteCosEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteExp2Estimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteExpEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteInvSqrtEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteLog2Estimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteLogEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteSinEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteSlerpEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteSqrtEstimate.h>
#include <ThirdParty/GTEngine/Mathematics/GteTanEstimate.h>

// GeometricPrimitives
#include <ThirdParty/GTEngine/Mathematics/GteAlignedBox.h>
#include <ThirdParty/GTEngine/Mathematics/GteArc2.h>
#include <ThirdParty/GTEngine/Mathematics/GteCapsule.h>
#include <ThirdParty/GTEngine/Mathematics/GteCircle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteCone.h>
#include <ThirdParty/GTEngine/Mathematics/GteConvexPolyhedron3.h>
#include <ThirdParty/GTEngine/Mathematics/GteCylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteEllipse3.h>
#include <ThirdParty/GTEngine/Mathematics/GteFrustum3.h>
#include <ThirdParty/GTEngine/Mathematics/GteHalfspace.h>
#include <ThirdParty/GTEngine/Mathematics/GteHyperellipsoid.h>
#include <ThirdParty/GTEngine/Mathematics/GteHyperplane.h>
#include <ThirdParty/GTEngine/Mathematics/GteHypersphere.h>
#include <ThirdParty/GTEngine/Mathematics/GteLine.h>
#include <ThirdParty/GTEngine/Mathematics/GteOrientedBox.h>
#include <ThirdParty/GTEngine/Mathematics/GtePolygon2.h>
#include <ThirdParty/GTEngine/Mathematics/GtePolyhedron3.h>
#include <ThirdParty/GTEngine/Mathematics/GteRay.h>
#include <ThirdParty/GTEngine/Mathematics/GteRectangle.h>
#include <ThirdParty/GTEngine/Mathematics/GteSector2.h>
#include <ThirdParty/GTEngine/Mathematics/GteSegment.h>
#include <ThirdParty/GTEngine/Mathematics/GteTetrahedron3.h>
#include <ThirdParty/GTEngine/Mathematics/GteTorus3.h>
#include <ThirdParty/GTEngine/Mathematics/GteTriangle.h>

// Interpolation
#include <ThirdParty/GTEngine/Mathematics/GteIntpAkima1.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpAkimaNonuniform1.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpAkimaUniform1.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpAkimaUniform2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpAkimaUniform3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpBicubic2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpBilinear2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpBSplineUniform.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpLinearNonuniform2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpLinearNonuniform3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpQuadraticNonuniform2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpSphere2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpThinPlateSpline2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpThinPlateSpline3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpTricubic3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpTrilinear3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntpVectorField2.h>

// Intersection
#include <ThirdParty/GTEngine/Mathematics/GteFIQuery.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox2AlignedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox2Circle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox2OrientedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox3Cone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrAlignedBox3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrArc2Arc2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrCapsule3Capsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrCircle2Arc2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrCircle2Circle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrConvexPolygonHyperplane.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrDisk2Sector2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrEllipse2Ellipse2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrEllipsoid3Ellipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace2Polygon2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3Capsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3Ellipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3Segment3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrHalfspace3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrIntervals.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2AlignedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2Arc2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2Circle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2Line2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2OrientedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2Ray2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2Segment2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine2Triangle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Capsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Cone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Ellipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Plane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrLine3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox2Circle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox2Cone2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox2OrientedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox2Sector2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox3Cone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox3Frustum3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrOrientedBox3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Capsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Circle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Ellipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Plane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrPlane3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2AlignedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2Arc2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2Circle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2OrientedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2Ray2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2Segment2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay2Triangle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Capsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Cone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Ellipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Plane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrRay3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment2AlignedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment2Arc2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment2Circle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment2OrientedBox2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment2Segment2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment2Triangle2.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3AlignedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Capsule3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Cone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Cylinder3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Ellipsoid3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Plane3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSegment3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSphere3Cone3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSphere3Frustum3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSphere3Sphere3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrSphere3Triangle3.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntrTriangle3OrientedBox3.h>
#include <ThirdParty/GTEngine/Mathematics/GteTIQuery.h>

// NumericalMethods
#include <ThirdParty/GTEngine/Mathematics/GteCholeskyDecomposition.h>
#include <ThirdParty/GTEngine/Mathematics/GteCubicRootsQR.h>
#include <ThirdParty/GTEngine/Mathematics/GteGaussNewtonMinimizer.h>
#include <ThirdParty/GTEngine/Mathematics/GteGaussianElimination.h>
#include <ThirdParty/GTEngine/Mathematics/GteIntegration.h>
#include <ThirdParty/GTEngine/Mathematics/GteLCPSolver.h>
#include <ThirdParty/GTEngine/Mathematics/GteLevenbergMarquardtMinimizer.h>
#include <ThirdParty/GTEngine/Mathematics/GteLinearSystem.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimize1.h>
#include <ThirdParty/GTEngine/Mathematics/GteMinimizeN.h>
#include <ThirdParty/GTEngine/Mathematics/GteOdeEuler.h>
#include <ThirdParty/GTEngine/Mathematics/GteOdeImplicitEuler.h>
#include <ThirdParty/GTEngine/Mathematics/GteOdeMidpoint.h>
#include <ThirdParty/GTEngine/Mathematics/GteOdeRungeKutta4.h>
#include <ThirdParty/GTEngine/Mathematics/GteOdeSolver.h>
#include <ThirdParty/GTEngine/Mathematics/GteQuarticRootsQR.h>
#include <ThirdParty/GTEngine/Mathematics/GteRootsBisection.h>
#include <ThirdParty/GTEngine/Mathematics/GteRootsBrentsMethod.h>
#include <ThirdParty/GTEngine/Mathematics/GteRootsPolynomial.h>
#include <ThirdParty/GTEngine/Mathematics/GteSingularValueDecomposition.h>
#include <ThirdParty/GTEngine/Mathematics/GteSymmetricEigensolver.h>
#include <ThirdParty/GTEngine/Mathematics/GteSymmetricEigensolver2x2.h>
#include <ThirdParty/GTEngine/Mathematics/GteSymmetricEigensolver3x3.h>
#include <ThirdParty/GTEngine/Mathematics/GteUnsymmetricEigenvalues.h>

// Projection
#include <ThirdParty/GTEngine/Mathematics/GteProjection.h>

// SIMD (removed due to compile issues + not needed currently)
// #if defined(__MSWINDOWS__) && !defined(MINGW)
// #include <ThirdParty/GTEngine/Mathematics/MSW/GteIntelSSE.h>
// #include <ThirdParty/GTEngine/Mathematics/MSW/GteCPUQueryInstructions.h>
// #endif

