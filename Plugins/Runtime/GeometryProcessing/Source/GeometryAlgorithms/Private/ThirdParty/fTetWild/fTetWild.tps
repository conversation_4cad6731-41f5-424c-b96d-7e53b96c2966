<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name> fTetWild </Name>
  <Location> Engine\Plugins\Runtime\GeometryProcessing\Source\GeometryAlgorithms\Private\ThirdParty </Location>
  <Function> fTetWild processes surface (triangle) meshes to create 'conforming' volumetric tetrahedral meshes, which we would use to help set up deformable flesh/muscle simulations for the physics team. (And for additional geometry processing tasks later on.)</Function>
  <Eula>https://github.com/wildmeshing/fTetWild/blob/master/LICENSE.MPL2 </Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>