// Copyright Epic Games, Inc. All Rights Reserved.

#include "Trace/ChaosVDTraceProvider.h"

#include "ChaosVDModule.h"
#include "ChaosVDRecording.h"

#include "ChaosVisualDebugger/ChaosVDSerializedNameTable.h"
#include "ChaosVisualDebugger/ChaosVisualDebuggerTrace.h"

#include "Compression/OodleDataCompressionUtil.h"
#include "DataProcessors/ChaosVDAABBTreeDataProcessor.h"
#include "DataProcessors/ChaosVDCollisionChannelsInfoDataProcessor.h"
#include "DataProcessors/ChaosVDDebugDrawBoxDataProcessor.h"
#include "DataProcessors/ChaosVDDebugDrawImplicitObjectDataProcessor.h"
#include "DataProcessors/ChaosVDDebugDrawLineDataProcessor.h"
#include "DataProcessors/ChaosVDDebugDrawSphereDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDCharacterGroundConstraintDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDConstraintDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDJointConstraintDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDMidPhaseDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDSceneQueryDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDSceneQueryVisitDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDSerializedNameEntryDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDTraceImplicitObjectProcessor.h"
#include "Trace/DataProcessors/ChaosVDTraceParticleDataProcessor.h"
#include "Trace/DataProcessors/ChaosVDArchiveHeaderProcessor.h"
#include "Trace/DataProcessors/ChaosVDDataProcessorBase.h"

namespace Chaos::VisualDebugger::Cvars
{
	static int32 MaxFramesToQueue = 2;
	static FAutoConsoleVariableRef CVarChaosVDMaxFramesToQueue(
		TEXT("p.Chaos.VD.Tool.MaxFramesToQueue"),
		MaxFramesToQueue,
		TEXT("The max number of game frames we will queue before processing them while the trace files is being loaded."));
}

FName FChaosVDTraceProvider::ProviderName("ChaosVDProvider");

FChaosVDTraceProvider::FChaosVDTraceProvider(TraceServices::IAnalysisSession& InSession): Session(InSession)
{
	DefaultHeaderData = Chaos::VisualDebugger::FChaosVDArchiveHeader::Current();
}

void FChaosVDTraceProvider::CreateRecordingInstanceForSession(const FString& InSessionName)
{
	DeleteRecordingInstanceForSession();

	InternalRecording = MakeShared<FChaosVDRecording>();
	InternalRecording->SessionName = InSessionName;
}

void FChaosVDTraceProvider::DeleteRecordingInstanceForSession()
{
	InternalRecording.Reset();
}

void FChaosVDTraceProvider::StartSolverFrame(const int32 InSolverID, FChaosVDSolverFrameData&& FrameData)
{
	if (!InternalRecording.IsValid())
	{
		return;
	}

	if (FChaosVDSolverFrameData* SolveFrameData = CurrentSolverFramesByID.Find(InSolverID))
	{
		InternalRecording->AddFrameForSolver(InSolverID, MoveTemp(*SolveFrameData));
		CurrentSolverFramesByID[InSolverID] = FrameData;
	}
	else
	{
		CurrentSolverFramesByID.Add(InSolverID, FrameData);
	}
}

void FChaosVDTraceProvider::GetAvailablePendingSolverIDsAtGameFrame(const TSharedRef<FChaosVDGameFrameData>& InProcessedGameFrameData, TArray<int32, TInlineAllocator<16>>& OutSolverIDs)
{
	for (const TPair<int32, FChaosVDSolverFrameData>& FrameDataWithSolverID : CurrentSolverFramesByID)
	{
		if (FrameDataWithSolverID.Value.FrameCycle < InProcessedGameFrameData->FirstCycle)
		{
			OutSolverIDs.Add(FrameDataWithSolverID.Key);
		}
	}
}

void FChaosVDTraceProvider::CommitProcessedGameFramesToRecording()
{
	TArray<int32, TInlineAllocator<16>> SolverIDs;

	// The Game Frame events are not generated by CVD trace code, and we don't have control over them.
	// we use them as general timestamps.
	// These are generated even when no solvers are available (specially in PIE), so we need to discard any game frame that will not resolve to a solver frame
	// Physics Frames and GT frames lifetimes might not align with async physics enabled, so to make sure we have all the solver data for that time range, we queue a handful of game frames before processing them.

	// TODO: This empty frame logic trimming is no longer 100% correct since we added support for async physics and support for recording GT specific data.
	// We need to find a better way to trim out empty data. Jira for Tracking UE-227365
	if (CurrentGameFrameQueueSize > Chaos::VisualDebugger::Cvars::MaxFramesToQueue)
	{
		TSharedPtr<FChaosVDGameFrameData> ProcessedGameFrameData;
		DeQueueGameFrameForProcessing(ProcessedGameFrameData);

		if (StartLastCommitedFrameTimeSeconds == 0.0)
		{
			StartLastCommitedFrameTimeSeconds = FPlatformTime::Seconds();
		}

		if (ProcessedGameFrameData.IsValid())
		{
			InternalRecording->GetAvailableSolverIDsAtGameFrame(*ProcessedGameFrameData, SolverIDs);

			// Is it possible that the solver data is not commited to the recording yet as it is being processed.
			// Usually this happens on recordings with Async Physics
			if (SolverIDs.IsEmpty())
			{
				GetAvailablePendingSolverIDsAtGameFrame(ProcessedGameFrameData.ToSharedRef(), SolverIDs);
			}

			// Any frame that is marked as dirty, or has any kind of solver data is considered relevant. Otherwise, we can trim it from the recording
			// by just ignoring it
			const bool bHasRelevantCVDData = !SolverIDs.IsEmpty() || ProcessedGameFrameData->IsDirty();
			if (bHasRelevantCVDData)
			{
				InternalRecording->AddGameFrameData(*ProcessedGameFrameData);	
			}
		}	
	}

	SolverIDs.Reset();
}

void FChaosVDTraceProvider::StartGameFrame(const TSharedPtr<FChaosVDGameFrameData>& InFrameData)
{
	if (!InternalRecording.IsValid())
	{
		return;
	}

	CommitProcessedGameFramesToRecording();

	EnqueueGameFrameForProcessing(InFrameData);
}

FChaosVDSolverFrameData* FChaosVDTraceProvider::GetCurrentSolverFrame(const int32 InSolverID)
{
	if (FChaosVDSolverFrameData* SolveFrameData = CurrentSolverFramesByID.Find(InSolverID))
	{
		return SolveFrameData;
	}

	return nullptr;
}

TWeakPtr<FChaosVDGameFrameData> FChaosVDTraceProvider::GetCurrentGameFrame()
{
	return CurrentGameFrame;
}

FChaosVDBinaryDataContainer& FChaosVDTraceProvider::FindOrAddUnprocessedData(const int32 DataID)
{
	if (const TSharedPtr<FChaosVDBinaryDataContainer>* UnprocessedData = UnprocessedDataByID.Find(DataID))
	{
		check(UnprocessedData->IsValid());
		return *UnprocessedData->Get();
	}
	else
	{
		const TSharedPtr<FChaosVDBinaryDataContainer> DataContainer = MakeShared<FChaosVDBinaryDataContainer>(DataID);
		UnprocessedDataByID.Add(DataID, DataContainer);
		return *DataContainer.Get();
	}
}

bool FChaosVDTraceProvider::ProcessBinaryData(const int32 DataID)
{
	RegisterDefaultDataProcessorsIfNeeded();

	if (const TSharedPtr<FChaosVDBinaryDataContainer>* UnprocessedDataPtr = UnprocessedDataByID.Find(DataID))
	{
		const TSharedPtr<FChaosVDBinaryDataContainer> UnprocessedData = *UnprocessedDataPtr;
		if (UnprocessedData.IsValid())
		{
			UnprocessedData->bIsReady = true;

			const TArray<uint8>* RawData = nullptr;
			TArray<uint8> UncompressedData;
			if (UnprocessedData->bIsCompressed)
			{
				UncompressedData.Reserve(UnprocessedData->UncompressedSize);
				FOodleCompressedArray::DecompressToTArray(UncompressedData, UnprocessedData->RawData);
				RawData = &UncompressedData;
			}
			else
			{
				RawData = &UnprocessedData->RawData;
			}

			if (TSharedPtr<FChaosVDDataProcessorBase>* DataProcessorPtrPtr = RegisteredDataProcessors.Find(UnprocessedData->TypeName))
			{
				if (TSharedPtr<FChaosVDDataProcessorBase> DataProcessorPtr = *DataProcessorPtrPtr)
				{
					if (ensure(DataProcessorPtr->ProcessRawData(*RawData)))
					{
						UnprocessedDataByID.Remove(DataID);
						return true;
					}
					else
					{
						UE_LOG(LogChaosVDEditor, Warning, TEXT("[%s] Failed to serialize Binary Data with ID [%d] | Type [%s]"), ANSI_TO_TCHAR(__FUNCTION__), DataID, *UnprocessedData->TypeName);
					}
				}
			}
			else
			{
				UE_LOG(LogChaosVDEditor, Warning, TEXT("[%s] Data processor for type [%s] not found"), ANSI_TO_TCHAR(__FUNCTION__), *UnprocessedData->TypeName);
			}
		}

		UnprocessedDataByID.Remove(DataID);
	}

	return false;
}

TSharedPtr<FChaosVDRecording> FChaosVDTraceProvider::GetRecordingForSession() const
{
	return InternalRecording;
}

void FChaosVDTraceProvider::RegisterDataProcessor(TSharedPtr<FChaosVDDataProcessorBase> InDataProcessor)
{
	RegisteredDataProcessors.Add(InDataProcessor->GetCompatibleTypeName(), InDataProcessor);
}

void FChaosVDTraceProvider::HandleAnalysisComplete()
{
	UnprocessedDataByID.Reset();

	UE_LOG(LogChaosVDEditor, Log, TEXT("Trace Analysis complete for session [%s] | Calculating data loaded stats..."), Session.GetName());

	static const FNumberFormattingOptions SizeFormattingOptions = FNumberFormattingOptions().SetMinimumFractionalDigits(2).SetMaximumFractionalDigits(2);

	uint64 TotalBytes = 0;
	for (const TPair<FStringView, TSharedPtr<FChaosVDDataProcessorBase>>& DataProcessor : RegisteredDataProcessors)
	{
		if (DataProcessor.Value)
		{
			uint64 ProcessedBytes = DataProcessor.Value->GetProcessedBytes();
			TotalBytes += ProcessedBytes;
			UE_LOG(LogChaosVDEditor, Log, TEXT("Data loaded for type [%s]  => [%s] "), DataProcessor.Key.IsEmpty() ? TEXT("Invalid") : DataProcessor.Key.GetData(), *FText::AsMemory(ProcessedBytes, &SizeFormattingOptions,nullptr, EMemoryUnitStandard::IEC).ToString());
		}
	}

	if (TSharedPtr<FChaosVDRecording> Recording = GetRecordingForSession())
	{
		double TotalTimeProcessingFrames = FPlatformTime::Seconds() - StartLastCommitedFrameTimeSeconds;

		int32 NumOfGameFramesProcessed = Recording->GetAvailableGameFramesNumber();
		double AvgTimePerFrameSeconds = TotalTimeProcessingFrames / NumOfGameFramesProcessed;
		
		UE_LOG(LogChaosVDEditor, Log, TEXT(" [%d] Game frames Processed at [%f] ms per frame on average"), NumOfGameFramesProcessed, AvgTimePerFrameSeconds * 1000.0);
	}

	UE_LOG(LogChaosVDEditor, Log, TEXT("Total size of loaded data => [%s]"), *FText::AsMemory(TotalBytes, &SizeFormattingOptions,nullptr, EMemoryUnitStandard::IEC).ToString());
}

FChaosVDStepData* FChaosVDTraceProvider::GetCurrentSolverStageDataForCurrentFrame(int32 SolverID, EChaosVDSolverStageAccessorFlags Flags)
{
	auto CreateInBetweenSolverStage = [](FChaosVDSolverFrameData& InFrameData)
	{
		// Add an empty step. It will be filled out by the particle (and later on other objects/elements) events
		FChaosVDStepData& SolverStageData = InFrameData.SolverSteps.AddDefaulted_GetRef();
		SolverStageData.StepName = TEXT("Between Stage Data");
		EnumAddFlags(SolverStageData.StageFlags, EChaosVDSolverStageFlags::Open);

		return &SolverStageData;
	};

	if (FChaosVDSolverFrameData* FrameData = GetCurrentSolverFrame(SolverID))
	{
		if (FrameData->SolverSteps.Num() == 0)
		{
			if (EnumHasAnyFlags(Flags, EChaosVDSolverStageAccessorFlags::CreateNewIfEmpty))
			{
				return CreateInBetweenSolverStage(*FrameData);
			}
		}

		FChaosVDStepData& CurrentSolverStage = FrameData->SolverSteps.Last();
		if (EnumHasAnyFlags(CurrentSolverStage.StageFlags, EChaosVDSolverStageFlags::Open))
		{
			return &CurrentSolverStage;
		}

		if (EnumHasAnyFlags(Flags, EChaosVDSolverStageAccessorFlags::CreateNewIfClosed))
		{
			return CreateInBetweenSolverStage(*FrameData);
		}
	}

	return nullptr;
}

void FChaosVDTraceProvider::RegisterDefaultDataProcessorsIfNeeded()
{
	if (bDefaultDataProcessorsRegistered)
	{
		return;
	}
	
	TSharedPtr<FChaosVDTraceImplicitObjectProcessor> ImplicitObjectProcessor = MakeShared<FChaosVDTraceImplicitObjectProcessor>();
	ImplicitObjectProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(ImplicitObjectProcessor);

	TSharedPtr<FChaosVDTraceParticleDataProcessor> ParticleDataProcessor = MakeShared<FChaosVDTraceParticleDataProcessor>();
	ParticleDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(ParticleDataProcessor);

	TSharedPtr<FChaosVDMidPhaseDataProcessor> MidPhaseDataProcessor = MakeShared<FChaosVDMidPhaseDataProcessor>();
	MidPhaseDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(MidPhaseDataProcessor);

	TSharedPtr<FChaosVDConstraintDataProcessor> ConstraintDataProcessor = MakeShared<FChaosVDConstraintDataProcessor>();
	ConstraintDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(ConstraintDataProcessor);

	TSharedPtr<FChaosVDSceneQueryDataProcessor> SceneQueryDataProcessor = MakeShared<FChaosVDSceneQueryDataProcessor>();
	SceneQueryDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(SceneQueryDataProcessor);

	TSharedPtr<FChaosVDSceneQueryVisitDataProcessor> SceneQueryVisitDataProcessor = MakeShared<FChaosVDSceneQueryVisitDataProcessor>();
	SceneQueryVisitDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(SceneQueryVisitDataProcessor);

	TSharedPtr<FChaosVDSerializedNameEntryDataProcessor> NameEntryDataProcessor = MakeShared<FChaosVDSerializedNameEntryDataProcessor>();
	NameEntryDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(NameEntryDataProcessor);
	
	TSharedPtr<FChaosVDJointConstraintDataProcessor> JointConstraintDataProcessor = MakeShared<FChaosVDJointConstraintDataProcessor>();
	JointConstraintDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(JointConstraintDataProcessor);

	TSharedPtr<FChaosVDCharacterGroundConstraintDataProcessor> CharacterGroundConstraintDataProcessor = MakeShared<FChaosVDCharacterGroundConstraintDataProcessor>();
	CharacterGroundConstraintDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(CharacterGroundConstraintDataProcessor);

	TSharedPtr<FChaosVDArchiveHeaderProcessor> ArchiveHeaderDataProcessor = MakeShared<FChaosVDArchiveHeaderProcessor>();
	ArchiveHeaderDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(ArchiveHeaderDataProcessor);

	TSharedPtr<FChaosVDAABBTreeDataProcessor> AABBTreeDataProcessor = MakeShared<FChaosVDAABBTreeDataProcessor>();
	AABBTreeDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(AABBTreeDataProcessor);

	TSharedPtr<FChaosVDDebugDrawBoxDataProcessor> DebugDrawBoxesDataProcessor = MakeShared<FChaosVDDebugDrawBoxDataProcessor>();
	DebugDrawBoxesDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(DebugDrawBoxesDataProcessor);

	TSharedPtr<FChaosVDDebugDrawLineDataProcessor> DebugDrawLinesDataProcessor = MakeShared<FChaosVDDebugDrawLineDataProcessor>();
	DebugDrawLinesDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(DebugDrawLinesDataProcessor);

	TSharedPtr<FChaosVDDebugDrawSphereDataProcessor> DebugDrawSpheresDataProcessor = MakeShared<FChaosVDDebugDrawSphereDataProcessor>();
	DebugDrawSpheresDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(DebugDrawSpheresDataProcessor);

	TSharedPtr<FChaosVDDebugDrawImplicitObjectDataProcessor> DebugDrawImplicitObjectDataProcessor = MakeShared<FChaosVDDebugDrawImplicitObjectDataProcessor>();
	DebugDrawImplicitObjectDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(DebugDrawImplicitObjectDataProcessor);
	
	TSharedPtr<FChaosVDCollisionChannelsInfoDataProcessor> CollisionChannelsInfoDataProcessor = MakeShared<FChaosVDCollisionChannelsInfoDataProcessor>();
	CollisionChannelsInfoDataProcessor->SetTraceProvider(AsShared());
	RegisterDataProcessor(CollisionChannelsInfoDataProcessor);

	bDefaultDataProcessorsRegistered = true;
}

void FChaosVDTraceProvider::EnqueueGameFrameForProcessing(const TSharedPtr<FChaosVDGameFrameData>& FrameData)
{
	CurrentGameFrame = FrameData;
	CurrentGameFrameQueue.Enqueue(FrameData);
	CurrentGameFrameQueueSize++;
}

void FChaosVDTraceProvider::DeQueueGameFrameForProcessing(TSharedPtr<FChaosVDGameFrameData>& OutFrameData)
{
	CurrentGameFrameQueue.Dequeue(OutFrameData);
	CurrentGameFrameQueueSize--;
}
