// Copyright Epic Games, Inc. All Rights Reserved.

// Must match LumenRadianceCacheInternal.h
#define RADIANCE_CACHE_TRACE_TILE_SIZE_2D 8
#define RADIANCE_CACHE_TRACE_TILE_SIZE_1D (RADIANCE_CACHE_TRACE_TILE_SIZE_2D * RADIANCE_CACHE_TRACE_TILE_SIZE_2D)
#define TRACE_TILE_GROUP_STRIDE 128

void GetProbeTraceDataNoOffset(float4 InProbeTraceData, out float3 ProbeWorldCenter, out uint ClipmapIndex, out uint ProbeIndex)
{
	ProbeWorldCenter = InProbeTraceData.xyz;
	uint PackedW = asuint(InProbeTraceData.w);
	ClipmapIndex = PackedW >> 24;
	ProbeIndex = PackedW & 0xFFFFFF;
}

void GetProbeTraceData(float4 InProbeTraceData, out float3 ProbeWorldCenter, out uint ClipmapIndex, out uint ProbeIndex)
{
	float3 ProbeWorldCenterNoOffset;
	GetProbeTraceDataNoOffset(InProbeTraceData, ProbeWorldCenterNoOffset, ClipmapIndex, ProbeIndex);
	ProbeWorldCenter = ProbeWorldCenterNoOffset;// + ProbeWorldOffset[ProbeIndex].xyz; //#lumen_todo: start using it or completely remove
}

uint GetProbeIndexFromProbeTraceData(float4 InProbeTraceData)
{
	float3 ProbeWorldCenter;
	uint ClipmapIndex;
	uint ProbeIndex;
	GetProbeTraceDataNoOffset(InProbeTraceData, ProbeWorldCenter, ClipmapIndex, ProbeIndex);
	return ProbeIndex;
}

uint2 PackTraceTileInfo(uint2 TraceTileCoord, uint Level, uint ProbeTraceIndex)
{
	return uint2((TraceTileCoord.x & 0xFF) | ((TraceTileCoord.y & 0xFF) << 8) | ((Level & 0xFF) << 16), ProbeTraceIndex);
}

uint2 UnpackTraceTileInfo(uint2 TraceTileInfo)
{
	uint2 TraceTileCoord;
	TraceTileCoord.x = TraceTileInfo.x & 0xFF;
	TraceTileCoord.y = (TraceTileInfo.x >> 8) & 0xFF;
	return TraceTileCoord;
}

void UnpackTraceTileInfo(uint2 TraceTileInfo, out uint2 TraceTileCoord, out uint Level, out uint ProbeTraceIndex)
{
	TraceTileCoord = UnpackTraceTileInfo(TraceTileInfo);
	Level = (TraceTileInfo.x >> 16) & 0xFF;
	ProbeTraceIndex = TraceTileInfo.y;
}
