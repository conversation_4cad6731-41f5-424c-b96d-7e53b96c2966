// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "RectLight.ush"

struct FAreaLight
{
	float		SphereSinAlpha;
	float		SphereSinAlphaSoft;
	float		LineCosSubtended;

	float3		FalloffColor;

	FRect		Rect;
	FRectTexture Texture;

	uint		IsRectAndDiffuseMicroReflWeight;
};

struct FAreaLightIntegrateContext
{
	FAreaLight AreaLight;
	float3 L;
	float NoL;
	float Falloff;
};

void SetIsRectLight(inout FAreaLight AreaLight, bool bIsRectLight)
{
	AreaLight.IsRectAndDiffuseMicroReflWeight = (AreaLight.IsRectAndDiffuseMicroReflWeight & 0xFFFFFFFE) | (bIsRectLight ? 0x1 : 0x0);
}

bool IsRectLight(FAreaLight AreaLight)
{
	return (AreaLight.IsRectAndDiffuseMicroReflWeight & 0x00000001) == 0x1;
}

void SetAreaLightDiffuseMicroReflWeight(inout FAreaLight AreaLight, float Weight)
{
	// We store the 32 bits float as 31 bits by droping the sign bit.
	AreaLight.IsRectAndDiffuseMicroReflWeight = (AreaLight.IsRectAndDiffuseMicroReflWeight & 0x00000001) | (asuint(Weight) << 1);
}

float GetAreaLightDiffuseMicroReflWeight(FAreaLight AreaLight)
{
	return asfloat(AreaLight.IsRectAndDiffuseMicroReflWeight >> 1);
}

bool IsAreaLight(FAreaLight AreaLight)
{
	return IsRectLight(AreaLight) || GetAreaLightDiffuseMicroReflWeight(AreaLight) < 1.0f;
}

FAreaLightIntegrateContext InitAreaLightIntegrateContext()
{
	// Manual initialization of the area light as the compiler is unhappy otherwise
	FAreaLightIntegrateContext Out;
	Out.AreaLight.SphereSinAlpha = 0;
	Out.AreaLight.SphereSinAlphaSoft = 0;
	Out.AreaLight.LineCosSubtended = 0;
	Out.AreaLight.FalloffColor = 0;
	Out.AreaLight.Rect = (FRect)0;
	Out.AreaLight.IsRectAndDiffuseMicroReflWeight = 0;
	Out.AreaLight.Texture = InitRectTexture();
	Out.L = 0;
	Out.NoL = 0;
	Out.Falloff = 0;
	return Out;
}
