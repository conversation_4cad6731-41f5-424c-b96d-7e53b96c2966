// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
	DownsampleVertexShader_F32.usf: Filter vertex shader source.
=============================================================================*/

void main(
	float4 Position : POSITION,
	float2 UV : TEXCOORD0,
	out float2 OutUV : TEXCOORD0,
	out float4 OutPosition : POSITION
	)
{
	OutPosition = Position;
	OutUV = UV;
}
