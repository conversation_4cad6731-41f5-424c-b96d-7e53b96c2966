// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Animation/AnimMontage.h"
#include "../InventoryItem.h"
#include "BlackTideAnimationComponent.generated.h"

class ABlack<PERSON>ideCharacter;
class UAnimInstance;

/**
 * Animation types for different actions
 */
UENUM(BlueprintType)
enum class EBlackTideAnimationType : uint8
{
	None			UMETA(DisplayName = "None"),
	Attack			UMETA(DisplayName = "Attack"),
	Gather			UMETA(DisplayName = "Gather"),
	Idle			UMETA(DisplayName = "Idle"),
	Equip			UMETA(DisplayName = "Equip"),
	Unequip			UMETA(DisplayName = "Unequip")
};

/**
 * Animation data structure for items
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FItemAnimationData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> AttackMontage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> GatherMontage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> IdleMontage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float AttackSpeed = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float GatherSpeed = 1.0f;

	FItemAnimationData()
	{
		AttackMontage = nullptr;
		GatherMontage = nullptr;
		IdleMontage = nullptr;
		AttackSpeed = 1.0f;
		GatherSpeed = 1.0f;
	}
};

/**
 * Delegates for animation events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAnimationStarted, EBlackTideAnimationType, AnimationType, float, Duration);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAnimationCompleted, EBlackTideAnimationType, AnimationType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAnimationCancelled, EBlackTideAnimationType, AnimationType);

/**
 * Animation management component for BlackTide characters
 * Handles item-specific animations, montage playback, and animation events
 */
UCLASS(ClassGroup=(BlackTide), meta=(BlueprintSpawnableComponent))
class BLACKTIDE_API UBlackTideAnimationComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UBlackTideAnimationComponent();

protected:
	virtual void BeginPlay() override;

public:
	// Animation events
	UPROPERTY(BlueprintAssignable, Category = "Animation Events")
	FOnAnimationStarted OnAnimationStarted;

	UPROPERTY(BlueprintAssignable, Category = "Animation Events")
	FOnAnimationCompleted OnAnimationCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Animation Events")
	FOnAnimationCancelled OnAnimationCancelled;

	// Core animation functions
	UFUNCTION(BlueprintCallable, Category = "Animation")
	bool PlayItemAnimation(const FInventoryItem& Item, EBlackTideAnimationType AnimationType);

	UFUNCTION(BlueprintCallable, Category = "Animation")
	bool PlayMontage(UAnimMontage* Montage, float PlayRate = 1.0f);

	UFUNCTION(BlueprintCallable, Category = "Animation")
	void StopCurrentAnimation();

	// Multicast animation functions
	UFUNCTION(NetMulticast, Reliable, Category = "Animation")
	void MulticastPlayMontage(UAnimMontage* Montage, float PlayRate);

	UFUNCTION(NetMulticast, Reliable, Category = "Animation")
	void MulticastStopAnimation();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Animation")
	bool IsPlayingAnimation() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Animation")
	EBlackTideAnimationType GetCurrentAnimationType() const { return CurrentAnimationType; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Animation")
	float GetAnimationProgress() const;

	// Item-specific animation getters
	UFUNCTION(BlueprintCallable, Category = "Animation")
	UAnimMontage* GetItemAttackMontage(const FInventoryItem& Item) const;

	UFUNCTION(BlueprintCallable, Category = "Animation")
	UAnimMontage* GetItemGatherMontage(const FInventoryItem& Item) const;

	UFUNCTION(BlueprintCallable, Category = "Animation")
	float GetItemAttackSpeed(const FInventoryItem& Item) const;

protected:
	// Cached references
	UPROPERTY()
	ABlackTideCharacter* OwnerCharacter;

	UPROPERTY()
	UAnimInstance* AnimInstance;

	// Current animation state
	UPROPERTY()
	UAnimMontage* CurrentMontage;

	UPROPERTY()
	EBlackTideAnimationType CurrentAnimationType;

	float AnimationStartTime;

	// Animation event handlers
	UFUNCTION()
	void OnMontageEnded(UAnimMontage* Montage, bool bInterrupted);

	// Helper functions
	FItemAnimationData GetAnimationDataForItem(const FInventoryItem& Item) const;
	UAnimMontage* LoadMontageFromSoftPtr(const TSoftObjectPtr<UAnimMontage>& SoftPtr) const;
};
