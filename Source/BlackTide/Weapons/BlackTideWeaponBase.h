// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "../InventoryItem.h"
#include "BlackTideWeaponBase.generated.h"

class UStaticMeshComponent;
class UBoxComponent;
class USphereComponent;
class ABlack<PERSON><PERSON><PERSON>haracter;

/**
 * Weapon types for different categories
 */
UENUM(BlueprintType)
enum class EWeaponCategory : uint8
{
	None		UMETA(DisplayName = "None"),
	Melee		UMETA(DisplayName = "Melee"),
	Ranged		UMETA(DisplayName = "Ranged"),
	Tool		UMETA(DisplayName = "Tool"),
	Throwable	UMETA(DisplayName = "Throwable")
};

/**
 * Weapon damage data
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FWeaponDamageData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float BaseDamage = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float ResourceDamage = 15.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float CriticalMultiplier = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float AttackSpeed = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float AttackRange = 150.0f;
};

/**
 * Delegates for weapon events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnWeaponHitTarget, AActor*, HitActor, FVector, HitLocation, float, Damage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponResourceHit, AActor*, ResourceActor, const FInventoryItem&, HarvestedItem);

/**
 * Base class for all weapons in BlackTide
 * Provides common functionality for melee, ranged, and tool weapons
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class BLACKTIDE_API ABlackTideWeaponBase : public AActor
{
	GENERATED_BODY()

public:
	ABlackTideWeaponBase();

protected:
	virtual void BeginPlay() override;

public:
	// Core components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* WeaponMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UBoxComponent* HitCollision;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* AttachPoint;

	// Weapon properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Settings")
	EWeaponCategory WeaponCategory = EWeaponCategory::Melee;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Settings")
	FString WeaponID = TEXT("DefaultWeapon");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Settings")
	FString WeaponName = TEXT("Default Weapon");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Settings")
	FWeaponDamageData DamageData;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Settings")
	TArray<FString> CompatibleResourceTypes;

	// Attack behavior settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Behavior")
	bool bEndAttackOnResourceHit = true; // End attack immediately when hitting resource

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Behavior")
	bool bEndAttackOnCombatHit = false; // Continue attack for combat (can hit multiple enemies)

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Behavior")
	float MaxAttackDuration = 2.0f; // Maximum time attack stays active

	// Debug settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	bool bShowCollisionDebug = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	float DebugDrawTime = 2.0f;

	// Animation settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> AttackMontage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> IdleMontage;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
	FOnWeaponHitTarget OnWeaponHitTarget;

	UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
	FOnWeaponResourceHit OnWeaponResourceHit;

	// Core weapon functions
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void StartAttack(ABlackTideCharacter* Wielder);

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void EndAttack();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void EnableHitDetection();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	void DisableHitDetection();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
	bool IsHitDetectionEnabled() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
	float GetBaseDamage() const { return DamageData.BaseDamage; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
	float GetAttackSpeed() const { return DamageData.AttackSpeed; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
	UAnimMontage* GetAttackMontage() const;

	// Resource compatibility
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Weapon")
	bool CanHarvestResource(const FString& ResourceType) const;

	// Debug functions
	UFUNCTION(BlueprintCallable, Category = "Debug")
	void ToggleCollisionDebug();

	UFUNCTION(BlueprintCallable, Category = "Debug")
	void SetCollisionDebug(bool bEnabled);

protected:
	// Current wielder
	UPROPERTY()
	ABlackTideCharacter* CurrentWielder;

	// Hit tracking
	TSet<AActor*> HitActorsThisSwing;
	float LastHitTime = 0.0f;
	bool bHitDetectionEnabled = false;

	// Hit event handlers
	UFUNCTION()
	virtual void OnHitCollisionHit(UPrimitiveComponent* HitComp, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit);

	UFUNCTION()
	virtual void OnHitCollisionBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

	// Hit processing
	virtual void ProcessHitEvent(AActor* HitActor, const FHitResult& Hit);
	virtual void ProcessHit(AActor* HitActor, const FHitResult& Hit);
	virtual void ProcessResourceHit(AActor* ResourceActor, const FHitResult& Hit);
	virtual void ProcessCombatHit(AActor* TargetActor, const FHitResult& Hit);

	// Helper functions
	virtual bool CanHitActor(AActor* Actor) const;
	virtual float CalculateDamage(AActor* Target) const;
	virtual bool IsResourceNode(AActor* Actor) const;
};
