// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "BlackTideWeaponBase.h"
#include "BlackTideMeleeWeapon.generated.h"

/**
 * Melee weapon types
 */
UENUM(BlueprintType)
enum class EMeleeWeaponType : uint8
{
	Axe			UMETA(DisplayName = "Axe"),
	Sword		UMETA(DisplayName = "Sword"),
	Hammer		UMETA(DisplayName = "Hammer"),
	<PERSON><PERSON>		UMETA(DisplayName = "Spear"),
	Dagger		UMETA(DisplayName = "Dagger"),
	Club		UMETA(DisplayName = "Club")
};

/**
 * Melee-specific weapon data
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FMeleeWeaponData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee")
	EMeleeWeaponType MeleeType = EMeleeWeaponType::Axe;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee")
	float SwingArc = 180.0f; // Degrees - wide arc for resource gathering (90° each side)

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee")
	float ComboWindow = 0.5f; // Seconds

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee")
	int32 MaxComboCount = 3;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee")
	bool bCanBlock = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee")
	float BlockDamageReduction = 0.5f;
};

/**
 * Specialized melee weapon class for BlackTide
 * Handles axes, swords, hammers, and other melee weapons
 */
UCLASS(BlueprintType, Blueprintable)
class BLACKTIDE_API ABlackTideMeleeWeapon : public ABlackTideWeaponBase
{
	GENERATED_BODY()

public:
	ABlackTideMeleeWeapon();

protected:
	virtual void BeginPlay() override;

public:
	// Melee-specific properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Settings")
	FMeleeWeaponData MeleeData;

	// Combo system
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
	TArray<TSoftObjectPtr<UAnimMontage>> ComboMontages;

	// Override base functions for melee-specific behavior
	virtual void StartAttack(ABlackTideCharacter* Wielder) override;
	virtual void EndAttack() override;

	// Melee-specific functions
	UFUNCTION(BlueprintCallable, Category = "Melee")
	void StartComboAttack(int32 ComboIndex);

	UFUNCTION(BlueprintCallable, Category = "Melee")
	void StartBlock();

	UFUNCTION(BlueprintCallable, Category = "Melee")
	void EndBlock();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Melee")
	bool CanStartCombo() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Melee")
	int32 GetCurrentComboCount() const { return CurrentComboCount; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Melee")
	EMeleeWeaponType GetMeleeType() const { return MeleeData.MeleeType; }

protected:
	// Combo tracking
	int32 CurrentComboCount = 0;
	float LastAttackTime = 0.0f;
	bool bIsBlocking = false;

	// Override hit processing for melee-specific logic
	virtual void ProcessHit(AActor* HitActor, const FHitResult& Hit) override;
	virtual void ProcessResourceHit(AActor* ResourceActor, const FHitResult& Hit) override;
	virtual void ProcessCombatHit(AActor* TargetActor, const FHitResult& Hit) override;

	// Melee-specific helpers
	bool IsInSwingArc(const FVector& HitLocation) const;
	float GetComboMultiplier() const;
	UAnimMontage* GetComboMontage(int32 ComboIndex) const;

	// Timer functions
	UFUNCTION()
	void ResetCombo();
};
