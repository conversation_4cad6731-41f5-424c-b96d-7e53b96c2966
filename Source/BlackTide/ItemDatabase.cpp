// Copyright Epic Games, Inc. All Rights Reserved.

#include "ItemDatabase.h"
#include "Actors/BlackTideWorldItem.h"
#include "Engine/Engine.h"

UItemDatabase::UItemDatabase()
{
	ItemDataTable = nullptr;
}

FInventoryItem UItemDatabase::CreateItem(const FString& ItemID, int32 StackSize)
{
	UE_LOG(LogTemp, Log, TEXT("ItemDatabase::CreateItem: Attempting to create '%s'"), *ItemID);

	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (ItemDef)
	{
		UE_LOG(LogTemp, Log, TEXT("ItemDatabase::CreateItem: Found '%s' in DataTable, using definition"), *ItemID);
		return CreateItemFromDefinition(ItemID, *ItemDef, StackSize);
	}

	// No fallbacks - everything must be in DataTable now
	UE_LOG(LogTemp, Error, TEXT("ItemDatabase: Item '%s' not found in DataTable! Add it to ItemDataTable."), *ItemID);
	return FInventoryItem(); // Return invalid item
}

FItemTableRow* UItemDatabase::GetItemDefinition(const FString& ItemID)
{
	// Load the item data table
	static UDataTable* ItemDataTable = nullptr;
	if (!ItemDataTable)
	{
		ItemDataTable = LoadObject<UDataTable>(nullptr, TEXT("/Game/Data/ItemDataTable.ItemDataTable"));
		if (!ItemDataTable)
		{
			UE_LOG(LogTemp, Warning, TEXT("ItemDatabase: Could not load ItemDataTable at /Game/Data/ItemDataTable"));
			return nullptr;
		}
		UE_LOG(LogTemp, Log, TEXT("ItemDatabase: Successfully loaded ItemDataTable"));
	}

	// Find the item definition
	FItemTableRow* ItemDef = ItemDataTable->FindRow<FItemTableRow>(FName(*ItemID), TEXT("ItemDatabase::GetItemDefinition"));
	if (!ItemDef)
	{
		UE_LOG(LogTemp, VeryVerbose, TEXT("ItemDatabase: Item '%s' not found in data table"), *ItemID);
	}

	return ItemDef;
}

TSoftObjectPtr<UTexture2D> UItemDatabase::LoadItemIcon(const FString& IconName)
{
	// Helper function to create icon paths consistently (updated to match your actual path)
	FString IconPath = FString::Printf(TEXT("/Game/UI/Icons/Items/Icon_%s.Icon_%s"), *IconName, *IconName);
	return TSoftObjectPtr<UTexture2D>(FSoftObjectPath(IconPath));
}

void UItemDatabase::DebugTestIconLoading(const FString& IconName)
{
	FString IconPath = FString::Printf(TEXT("/Game/UI/Icons/Items/Icon_%s.Icon_%s"), *IconName, *IconName);
	TSoftObjectPtr<UTexture2D> IconPtr = TSoftObjectPtr<UTexture2D>(FSoftObjectPath(IconPath));

	UE_LOG(LogTemp, Warning, TEXT("🔍 Testing icon loading for: %s"), *IconName);
	UE_LOG(LogTemp, Warning, TEXT("📁 Expected path: %s"), *IconPath);
	UE_LOG(LogTemp, Warning, TEXT("🔗 Icon pointer valid: %s"), IconPtr.IsValid() ? TEXT("YES") : TEXT("NO"));

	// Try to load synchronously for testing
	UTexture2D* LoadedTexture = IconPtr.LoadSynchronous();
	if (LoadedTexture)
	{
		UE_LOG(LogTemp, Warning, TEXT("✅ Icon loaded successfully! Size: %dx%d"),
			LoadedTexture->GetSizeX(), LoadedTexture->GetSizeY());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("❌ Failed to load icon texture!"));
		UE_LOG(LogTemp, Error, TEXT("💡 Check if file exists at: Content/UI/Icons/Items/Icon_%s.png"), *IconName);
	}
}

FInventoryItem UItemDatabase::CreateItemFromDefinition(const FString& ItemID, const FItemTableRow& Definition, int32 StackSize)
{
	FInventoryItem NewItem;
	NewItem.ItemID = ItemID;
	NewItem.Name = Definition.Name;
	NewItem.Description = Definition.Description;
	NewItem.ItemType = Definition.ItemType;
	NewItem.Icon = Definition.Icon;
	NewItem.MaxStackSize = Definition.MaxStackSize;
	NewItem.CurrentStackSize = FMath::Clamp(StackSize, 1, Definition.MaxStackSize);
	NewItem.Value = Definition.Value;
	NewItem.Weight = Definition.Weight;
	NewItem.MaxDurability = Definition.MaxDurability;
	NewItem.CurrentDurability = Definition.MaxDurability;
	NewItem.ConsumableEffect = Definition.ConsumableEffect;
	NewItem.UniqueID = FGuid::NewGuid();

	// Debug consumable effects from DataTable
	UE_LOG(LogTemp, Warning, TEXT("CreateItemFromDefinition: Item '%s' ConsumableEffect from DataTable - Health:%.1f, Hunger:%.1f, Thirst:%.1f, Stamina:%.1f"),
		*ItemID,
		Definition.ConsumableEffect.HealthRestore,
		Definition.ConsumableEffect.HungerRestore,
		Definition.ConsumableEffect.ThirstRestore,
		Definition.ConsumableEffect.StaminaRestore);

	// Debug icon loading
	UE_LOG(LogTemp, Log, TEXT("CreateItemFromDefinition: Item '%s' - Icon IsValid: %s, IsNull: %s"),
		*ItemID,
		Definition.Icon.IsValid() ? TEXT("YES") : TEXT("NO"),
		Definition.Icon.IsNull() ? TEXT("YES") : TEXT("NO"));

	if (!Definition.Icon.IsNull())
	{
		UE_LOG(LogTemp, Log, TEXT("CreateItemFromDefinition: Item '%s' has icon path: %s"),
			*ItemID, *Definition.Icon.ToString());

		// Force load the icon to make it valid
		if (!Definition.Icon.IsValid())
		{
			UTexture2D* LoadedIcon = Definition.Icon.LoadSynchronous();
			if (LoadedIcon)
			{
				UE_LOG(LogTemp, Log, TEXT("CreateItemFromDefinition: Successfully loaded icon for '%s'"), *ItemID);
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("CreateItemFromDefinition: Failed to load icon for '%s'"), *ItemID);
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("CreateItemFromDefinition: Item '%s' has NO ICON set in DataTable"), *ItemID);
	}

	return NewItem;
}

// Predefined items for testing
FInventoryItem UItemDatabase::CreateApple(int32 StackSize)
{
	FInventoryItem Apple;
	Apple.ItemID = TEXT("apple");
	Apple.Name = TEXT("Apple");
	Apple.Description = TEXT("A fresh red apple. Restores hunger and provides a small health boost.");
	Apple.ItemType = EItemType::Consumable;
	Apple.MaxStackSize = 10;
	Apple.CurrentStackSize = FMath::Clamp(StackSize, 1, 10);
	Apple.Value = 5;
	Apple.Weight = 0.2f;
	
	// Consumable effects
	Apple.ConsumableEffect.HealthRestore = 10.0f;
	Apple.ConsumableEffect.HungerRestore = 25.0f;
	Apple.ConsumableEffect.ThirstRestore = 5.0f;
	Apple.ConsumableEffect.ConsumptionTime = 2.0f;
	
	Apple.UniqueID = FGuid::NewGuid();
	return Apple;
}

FInventoryItem UItemDatabase::CreateWaterBottle(int32 StackSize)
{
	FInventoryItem WaterBottle;
	WaterBottle.ItemID = TEXT("water_bottle");
	WaterBottle.Name = TEXT("Water Bottle");
	WaterBottle.Description = TEXT("A bottle of clean water. Essential for survival.");
	WaterBottle.ItemType = EItemType::Consumable;
	WaterBottle.MaxStackSize = 5;
	WaterBottle.CurrentStackSize = FMath::Clamp(StackSize, 1, 5);
	WaterBottle.Value = 10;
	WaterBottle.Weight = 0.5f;
	
	// Consumable effects
	WaterBottle.ConsumableEffect.ThirstRestore = 50.0f;
	WaterBottle.ConsumableEffect.ConsumptionTime = 3.0f;
	
	WaterBottle.UniqueID = FGuid::NewGuid();
	return WaterBottle;
}

FInventoryItem UItemDatabase::CreateWood(int32 StackSize)
{
	FInventoryItem Wood;
	Wood.ItemID = TEXT("wood");
	Wood.Name = TEXT("Wood");
	Wood.Description = TEXT("Basic building material gathered from trees.");
	Wood.ItemType = EItemType::Resource;
	Wood.MaxStackSize = 50;
	Wood.CurrentStackSize = FMath::Clamp(StackSize, 1, 50);
	Wood.Value = 2;
	Wood.Weight = 0.1f;

	// Set icon directly
	Wood.Icon = TSoftObjectPtr<UTexture2D>(FSoftObjectPath(TEXT("/Game/UI/Icons/Items/Icon_Wood.Icon_Wood")));

	Wood.UniqueID = FGuid::NewGuid();
	return Wood;
}

FInventoryItem UItemDatabase::CreateStone(int32 StackSize)
{
	FInventoryItem Stone;
	Stone.ItemID = TEXT("stone");
	Stone.Name = TEXT("Stone");
	Stone.Description = TEXT("Solid stone material used for construction and crafting.");
	Stone.ItemType = EItemType::Resource;
	Stone.MaxStackSize = 30;
	Stone.CurrentStackSize = FMath::Clamp(StackSize, 1, 30);
	Stone.Value = 3;
	Stone.Weight = 0.3f;
	
	Stone.UniqueID = FGuid::NewGuid();
	return Stone;
}

FInventoryItem UItemDatabase::CreateIronAxe()
{
	FInventoryItem IronAxe;
	IronAxe.ItemID = TEXT("IronAxe");  // Fixed: Match the RequiredToolID in ResourceNode
	IronAxe.Name = TEXT("Iron Axe");
	IronAxe.Description = TEXT("A sturdy iron axe for chopping wood efficiently.");
	IronAxe.ItemType = EItemType::Tool;
	IronAxe.MaxStackSize = 1;  // Tools don't stack
	IronAxe.CurrentStackSize = 1;
	IronAxe.Value = 150;
	IronAxe.Weight = 2.5f;
	IronAxe.MaxDurability = 200.0f;
	IronAxe.CurrentDurability = 200.0f;

	// Set icon directly
	IronAxe.Icon = TSoftObjectPtr<UTexture2D>(FSoftObjectPath(TEXT("/Game/UI/Icons/Items/Icon_IronAxe.Icon_IronAxe")));

	IronAxe.UniqueID = FGuid::NewGuid();
	return IronAxe;
}

FInventoryItem UItemDatabase::CreateIronPickaxe()
{
	FInventoryItem IronPickaxe;
	IronPickaxe.ItemID = TEXT("IronPickaxe");  // Match the RequiredToolID in ResourceNode
	IronPickaxe.Name = TEXT("Iron Pickaxe");
	IronPickaxe.Description = TEXT("A sturdy iron pickaxe for mining stone and ore efficiently.");
	IronPickaxe.ItemType = EItemType::Tool;
	IronPickaxe.MaxStackSize = 1;  // Tools don't stack
	IronPickaxe.CurrentStackSize = 1;
	IronPickaxe.Value = 180;
	IronPickaxe.Weight = 3.0f;
	IronPickaxe.MaxDurability = 250.0f;
	IronPickaxe.CurrentDurability = 250.0f;

	// Set icon using helper function
	IronPickaxe.Icon = LoadItemIcon(TEXT("IronPickaxe"));

	IronPickaxe.UniqueID = FGuid::NewGuid();
	return IronPickaxe;
}

FInventoryItem UItemDatabase::CreateHealthPotion(int32 StackSize)
{
	FInventoryItem HealthPotion;
	HealthPotion.ItemID = TEXT("health_potion");
	HealthPotion.Name = TEXT("Health Potion");
	HealthPotion.Description = TEXT("A magical potion that instantly restores health.");
	HealthPotion.ItemType = EItemType::Consumable;
	HealthPotion.MaxStackSize = 3;
	HealthPotion.CurrentStackSize = FMath::Clamp(StackSize, 1, 3);
	HealthPotion.Value = 100;
	HealthPotion.Weight = 0.3f;

	// Powerful healing effect
	HealthPotion.ConsumableEffect.HealthRestore = 75.0f;
	HealthPotion.ConsumableEffect.ConsumptionTime = 1.5f;

	HealthPotion.UniqueID = FGuid::NewGuid();
	return HealthPotion;
}

// === FOOD & CONSUMABLES ===

FInventoryItem UItemDatabase::CreateCoconut(int32 StackSize)
{
	FInventoryItem Coconut;
	Coconut.ItemID = TEXT("coconut");
	Coconut.Name = TEXT("Coconut");
	Coconut.Description = TEXT("A fresh coconut. Provides both hydration and nutrition.");
	Coconut.ItemType = EItemType::Consumable;
	Coconut.MaxStackSize = 8;
	Coconut.CurrentStackSize = FMath::Clamp(StackSize, 1, 8);
	Coconut.Value = 15;
	Coconut.Weight = 0.8f;

	// Set icon using helper function (now with correct path)
	Coconut.Icon = LoadItemIcon(TEXT("Coconut"));

	// Coconut provides both thirst and hunger
	Coconut.ConsumableEffect.ThirstRestore = 35.0f;
	Coconut.ConsumableEffect.HungerRestore = 20.0f;
	Coconut.ConsumableEffect.ConsumptionTime = 4.0f;

	Coconut.UniqueID = FGuid::NewGuid();
	return Coconut;
}

FInventoryItem UItemDatabase::CreateJuccaRoot(int32 StackSize)
{
	FInventoryItem JuccaRoot;
	JuccaRoot.ItemID = TEXT("jucca_root");
	JuccaRoot.Name = TEXT("Jucca Root");
	JuccaRoot.Description = TEXT("A starchy root vegetable. Better when cooked.");
	JuccaRoot.ItemType = EItemType::Consumable;
	JuccaRoot.MaxStackSize = 15;
	JuccaRoot.CurrentStackSize = FMath::Clamp(StackSize, 1, 15);
	JuccaRoot.Value = 8;
	JuccaRoot.Weight = 0.3f;

	// Set icon (matching your file name Icon_Jucca)
	// Alternative: Use LoadItemIcon helper function
	JuccaRoot.Icon = LoadItemIcon(TEXT("Jucca"));

	// Manual path as backup (uncomment if helper doesn't work):
	// JuccaRoot.Icon = TSoftObjectPtr<UTexture2D>(FSoftObjectPath(TEXT("/Game/BlackTide/UI/Icons/Items/Icon_Jucca.Icon_Jucca")));

	// Raw jucca provides moderate hunger
	JuccaRoot.ConsumableEffect.HungerRestore = 15.0f;
	JuccaRoot.ConsumableEffect.ConsumptionTime = 3.0f;

	JuccaRoot.UniqueID = FGuid::NewGuid();
	return JuccaRoot;
}

FInventoryItem UItemDatabase::CreateCookedJucca(int32 StackSize)
{
	FInventoryItem CookedJucca;
	CookedJucca.ItemID = TEXT("cooked_jucca");
	CookedJucca.Name = TEXT("Cooked Jucca");
	CookedJucca.Description = TEXT("Properly cooked jucca root. Much more nutritious than raw.");
	CookedJucca.ItemType = EItemType::Consumable;
	CookedJucca.MaxStackSize = 12;
	CookedJucca.CurrentStackSize = FMath::Clamp(StackSize, 1, 12);
	CookedJucca.Value = 18;
	CookedJucca.Weight = 0.25f;

	// Set icon directly
	CookedJucca.Icon = TSoftObjectPtr<UTexture2D>(FSoftObjectPath(TEXT("/Game/UI/Icons/Items/Icon_CookedJucca.Icon_CookedJucca")));

	// Cooked jucca provides excellent hunger restoration
	CookedJucca.ConsumableEffect.HungerRestore = 40.0f;
	CookedJucca.ConsumableEffect.HealthRestore = 5.0f;
	CookedJucca.ConsumableEffect.ConsumptionTime = 2.5f;

	CookedJucca.UniqueID = FGuid::NewGuid();
	return CookedJucca;
}

FInventoryItem UItemDatabase::CreateBerries(int32 StackSize)
{
	FInventoryItem Berries;
	Berries.ItemID = TEXT("berries");
	Berries.Name = TEXT("Berries");
	Berries.Description = TEXT("Sweet wild berries. Provides quick energy and hydration.");
	Berries.ItemType = EItemType::Consumable;
	Berries.MaxStackSize = 20;
	Berries.CurrentStackSize = FMath::Clamp(StackSize, 1, 20);
	Berries.Value = 3;
	Berries.Weight = 0.05f;

	// Set icon
	Berries.Icon = LoadItemIcon(TEXT("Berries"));

	// Berries provide quick hunger and small thirst
	Berries.ConsumableEffect.HungerRestore = 8.0f;
	Berries.ConsumableEffect.ThirstRestore = 5.0f;
	Berries.ConsumableEffect.ConsumptionTime = 1.0f;

	Berries.UniqueID = FGuid::NewGuid();
	return Berries;
}

FInventoryItem UItemDatabase::CreateRawCrab(int32 StackSize)
{
	FInventoryItem RawCrab;
	RawCrab.ItemID = TEXT("raw_crab");
	RawCrab.Name = TEXT("Raw Crab");
	RawCrab.Description = TEXT("Fresh crab meat. Should be cooked before eating for best results.");
	RawCrab.ItemType = EItemType::Consumable;
	RawCrab.MaxStackSize = 6;
	RawCrab.CurrentStackSize = FMath::Clamp(StackSize, 1, 6);
	RawCrab.Value = 12;
	RawCrab.Weight = 0.4f;

	// Raw crab provides some hunger but less efficient
	RawCrab.ConsumableEffect.HungerRestore = 18.0f;
	RawCrab.ConsumableEffect.ConsumptionTime = 3.5f;

	RawCrab.UniqueID = FGuid::NewGuid();
	return RawCrab;
}

FInventoryItem UItemDatabase::CreateCookedCrab(int32 StackSize)
{
	FInventoryItem CookedCrab;
	CookedCrab.ItemID = TEXT("cooked_crab");
	CookedCrab.Name = TEXT("Cooked Crab");
	CookedCrab.Description = TEXT("Perfectly cooked crab meat. Delicious and nutritious.");
	CookedCrab.ItemType = EItemType::Consumable;
	CookedCrab.MaxStackSize = 5;
	CookedCrab.CurrentStackSize = FMath::Clamp(StackSize, 1, 5);
	CookedCrab.Value = 25;
	CookedCrab.Weight = 0.35f;

	// Cooked crab provides excellent hunger and health
	CookedCrab.ConsumableEffect.HungerRestore = 45.0f;
	CookedCrab.ConsumableEffect.HealthRestore = 15.0f;
	CookedCrab.ConsumableEffect.ConsumptionTime = 2.0f;

	CookedCrab.UniqueID = FGuid::NewGuid();
	return CookedCrab;
}

FInventoryItem UItemDatabase::CreateDirtyWater(int32 StackSize)
{
	FInventoryItem DirtyWater;
	DirtyWater.ItemID = TEXT("dirty_water");
	DirtyWater.Name = TEXT("Dirty Water");
	DirtyWater.Description = TEXT("Murky water from a questionable source. Should be boiled before drinking.");
	DirtyWater.ItemType = EItemType::Consumable;
	DirtyWater.MaxStackSize = 8;
	DirtyWater.CurrentStackSize = FMath::Clamp(StackSize, 1, 8);
	DirtyWater.Value = 3;
	DirtyWater.Weight = 0.6f;

	// Dirty water provides thirst but less efficiently
	DirtyWater.ConsumableEffect.ThirstRestore = 25.0f;
	DirtyWater.ConsumableEffect.ConsumptionTime = 2.5f;

	DirtyWater.UniqueID = FGuid::NewGuid();
	return DirtyWater;
}

FInventoryItem UItemDatabase::CreateBoiledWater(int32 StackSize)
{
	FInventoryItem BoiledWater;
	BoiledWater.ItemID = TEXT("boiled_water");
	BoiledWater.Name = TEXT("Boiled Water");
	BoiledWater.Description = TEXT("Clean, purified water. Safe to drink and very refreshing.");
	BoiledWater.ItemType = EItemType::Consumable;
	BoiledWater.MaxStackSize = 6;
	BoiledWater.CurrentStackSize = FMath::Clamp(StackSize, 1, 6);
	BoiledWater.Value = 12;
	BoiledWater.Weight = 0.6f;

	// Boiled water provides excellent thirst restoration
	BoiledWater.ConsumableEffect.ThirstRestore = 60.0f;
	BoiledWater.ConsumableEffect.HealthRestore = 5.0f;
	BoiledWater.ConsumableEffect.ConsumptionTime = 2.0f;

	BoiledWater.UniqueID = FGuid::NewGuid();
	return BoiledWater;
}

FInventoryItem UItemDatabase::CreateHerbs(int32 StackSize)
{
	FInventoryItem Herbs;
	Herbs.ItemID = TEXT("herbs");
	Herbs.Name = TEXT("Herbs");
	Herbs.Description = TEXT("Medicinal herbs found in the wild. Provides minor healing.");
	Herbs.ItemType = EItemType::Consumable;
	Herbs.MaxStackSize = 25;
	Herbs.CurrentStackSize = FMath::Clamp(StackSize, 1, 25);
	Herbs.Value = 8;
	Herbs.Weight = 0.02f;

	// Herbs provide health restoration
	Herbs.ConsumableEffect.HealthRestore = 20.0f;
	Herbs.ConsumableEffect.ConsumptionTime = 1.5f;

	Herbs.UniqueID = FGuid::NewGuid();
	return Herbs;
}

// === RESOURCES ===

FInventoryItem UItemDatabase::CreateWoodLog(int32 StackSize)
{
	FInventoryItem WoodLog;
	WoodLog.ItemID = TEXT("wood_log");
	WoodLog.Name = TEXT("Wood Log");
	WoodLog.Description = TEXT("A raw log from a tree. Can be processed into planks.");
	WoodLog.ItemType = EItemType::Resource;
	WoodLog.MaxStackSize = 30;
	WoodLog.CurrentStackSize = FMath::Clamp(StackSize, 1, 30);
	WoodLog.Value = 4;
	WoodLog.Weight = 0.8f;

	// Set icon using helper function
	WoodLog.Icon = LoadItemIcon(TEXT("WoodLog"));

	WoodLog.UniqueID = FGuid::NewGuid();
	return WoodLog;
}

FInventoryItem UItemDatabase::CreateWoodPlank(int32 StackSize)
{
	FInventoryItem WoodPlank;
	WoodPlank.ItemID = TEXT("wood_plank");
	WoodPlank.Name = TEXT("Wood Plank");
	WoodPlank.Description = TEXT("Processed wood plank. Essential for construction.");
	WoodPlank.ItemType = EItemType::Resource;
	WoodPlank.MaxStackSize = 40;
	WoodPlank.CurrentStackSize = FMath::Clamp(StackSize, 1, 40);
	WoodPlank.Value = 6;
	WoodPlank.Weight = 0.3f;

	WoodPlank.UniqueID = FGuid::NewGuid();
	return WoodPlank;
}

FInventoryItem UItemDatabase::CreateStoneChunk(int32 StackSize)
{
	FInventoryItem StoneChunk;
	StoneChunk.ItemID = TEXT("stone_chunk");
	StoneChunk.Name = TEXT("Stone Chunk");
	StoneChunk.Description = TEXT("A solid chunk of stone. Used for construction and tool making.");
	StoneChunk.ItemType = EItemType::Resource;
	StoneChunk.MaxStackSize = 25;
	StoneChunk.CurrentStackSize = FMath::Clamp(StackSize, 1, 25);
	StoneChunk.Value = 5;
	StoneChunk.Weight = 1.2f;

	StoneChunk.UniqueID = FGuid::NewGuid();
	return StoneChunk;
}

FInventoryItem UItemDatabase::CreateRope(int32 StackSize)
{
	FInventoryItem Rope;
	Rope.ItemID = TEXT("rope");
	Rope.Name = TEXT("Rope");
	Rope.Description = TEXT("Strong rope made from plant fibers. Essential for many crafting recipes.");
	Rope.ItemType = EItemType::Resource;
	Rope.MaxStackSize = 15;
	Rope.CurrentStackSize = FMath::Clamp(StackSize, 1, 15);
	Rope.Value = 12;
	Rope.Weight = 0.2f;

	Rope.UniqueID = FGuid::NewGuid();
	return Rope;
}

FInventoryItem UItemDatabase::CreateCoconutHusk(int32 StackSize)
{
	FInventoryItem CoconutHusk;
	CoconutHusk.ItemID = TEXT("coconut_husk");
	CoconutHusk.Name = TEXT("Coconut Husk");
	CoconutHusk.Description = TEXT("Fibrous husk from a coconut. Can be processed into useful materials.");
	CoconutHusk.ItemType = EItemType::Resource;
	CoconutHusk.MaxStackSize = 20;
	CoconutHusk.CurrentStackSize = FMath::Clamp(StackSize, 1, 20);
	CoconutHusk.Value = 2;
	CoconutHusk.Weight = 0.1f;

	CoconutHusk.UniqueID = FGuid::NewGuid();
	return CoconutHusk;
}

FInventoryItem UItemDatabase::CreatePlantFiber(int32 StackSize)
{
	FInventoryItem PlantFiber;
	PlantFiber.ItemID = TEXT("plant_fiber");
	PlantFiber.Name = TEXT("Plant Fiber");
	PlantFiber.Description = TEXT("Strong fibers extracted from plants. Used for crafting rope and textiles.");
	PlantFiber.ItemType = EItemType::Resource;
	PlantFiber.MaxStackSize = 50;
	PlantFiber.CurrentStackSize = FMath::Clamp(StackSize, 1, 50);
	PlantFiber.Value = 1;
	PlantFiber.Weight = 0.02f;

	PlantFiber.UniqueID = FGuid::NewGuid();
	return PlantFiber;
}

FInventoryItem UItemDatabase::CreateClay(int32 StackSize)
{
	FInventoryItem Clay;
	Clay.ItemID = TEXT("clay");
	Clay.Name = TEXT("Clay");
	Clay.Description = TEXT("Malleable clay found near water sources. Used for pottery and construction.");
	Clay.ItemType = EItemType::Resource;
	Clay.MaxStackSize = 20;
	Clay.CurrentStackSize = FMath::Clamp(StackSize, 1, 20);
	Clay.Value = 3;
	Clay.Weight = 0.5f;

	Clay.UniqueID = FGuid::NewGuid();
	return Clay;
}

FInventoryItem UItemDatabase::CreateFlint(int32 StackSize)
{
	FInventoryItem Flint;
	Flint.ItemID = TEXT("flint");
	Flint.Name = TEXT("Flint");
	Flint.Description = TEXT("Sharp flint stone. Essential for starting fires and making tools.");
	Flint.ItemType = EItemType::Resource;
	Flint.MaxStackSize = 30;
	Flint.CurrentStackSize = FMath::Clamp(StackSize, 1, 30);
	Flint.Value = 4;
	Flint.Weight = 0.1f;

	Flint.UniqueID = FGuid::NewGuid();
	return Flint;
}

FInventoryItem UItemDatabase::CreatePalmLeaf(int32 StackSize)
{
	FInventoryItem PalmLeaf;
	PalmLeaf.ItemID = TEXT("palm_leaf");
	PalmLeaf.Name = TEXT("Palm Leaf");
	PalmLeaf.Description = TEXT("Large palm leaf. Useful for shelter construction and crafting.");
	PalmLeaf.ItemType = EItemType::Resource;
	PalmLeaf.MaxStackSize = 25;
	PalmLeaf.CurrentStackSize = FMath::Clamp(StackSize, 1, 25);
	PalmLeaf.Value = 2;
	PalmLeaf.Weight = 0.1f;

	PalmLeaf.UniqueID = FGuid::NewGuid();
	return PalmLeaf;
}

// Resource gathering items
FInventoryItem UItemDatabase::CreateYuccaRoot(int32 StackSize)
{
	FInventoryItem YuccaRoot;
	YuccaRoot.ItemID = TEXT("yucca_root");
	YuccaRoot.Name = TEXT("Yucca Root");
	YuccaRoot.Description = TEXT("A fibrous root that can be eaten raw or cooked. Provides sustenance.");
	YuccaRoot.ItemType = EItemType::Consumable;
	YuccaRoot.MaxStackSize = 20;
	YuccaRoot.CurrentStackSize = FMath::Clamp(StackSize, 1, 20);
	YuccaRoot.Value = 3;
	YuccaRoot.Weight = 0.3f;

	// Set icon directly
	YuccaRoot.Icon = TSoftObjectPtr<UTexture2D>(FSoftObjectPath(TEXT("/Game/UI/Icons/Items/Icon_Jucca.Icon_Jucca")));

	// Consumable effects
	YuccaRoot.ConsumableEffect.HungerRestore = 15.0f;
	YuccaRoot.ConsumableEffect.ThirstRestore = 10.0f;
	YuccaRoot.ConsumableEffect.ConsumptionTime = 3.0f;

	UE_LOG(LogTemp, Warning, TEXT("CreateYuccaRoot: Set ConsumableEffect - Hunger:%.1f, Thirst:%.1f"),
		YuccaRoot.ConsumableEffect.HungerRestore, YuccaRoot.ConsumableEffect.ThirstRestore);

	YuccaRoot.UniqueID = FGuid::NewGuid();
	return YuccaRoot;
}

FInventoryItem UItemDatabase::CreateWildBerries(int32 StackSize)
{
	FInventoryItem Berries;
	Berries.ItemID = TEXT("berries");
	Berries.Name = TEXT("Wild Berries");
	Berries.Description = TEXT("Sweet wild berries. A quick source of energy and hydration.");
	Berries.ItemType = EItemType::Consumable;
	Berries.MaxStackSize = 30;
	Berries.CurrentStackSize = FMath::Clamp(StackSize, 1, 30);
	Berries.Value = 2;
	Berries.Weight = 0.1f;

	// Consumable effects
	Berries.ConsumableEffect.HungerRestore = 8.0f;
	Berries.ConsumableEffect.ThirstRestore = 12.0f;
	Berries.ConsumableEffect.ConsumptionTime = 1.5f;

	Berries.UniqueID = FGuid::NewGuid();
	return Berries;
}

FInventoryItem UItemDatabase::CreateWoodSticks(int32 StackSize)
{
	FInventoryItem Sticks;
	Sticks.ItemID = TEXT("sticks");
	Sticks.Name = TEXT("Sticks");
	Sticks.Description = TEXT("Small wooden sticks. Useful for crafting and starting fires.");
	Sticks.ItemType = EItemType::Resource;
	Sticks.MaxStackSize = 50;
	Sticks.CurrentStackSize = FMath::Clamp(StackSize, 1, 50);
	Sticks.Value = 1;
	Sticks.Weight = 0.1f;

	// Set icon using helper function
	Sticks.Icon = LoadItemIcon(TEXT("Sticks"));

	Sticks.UniqueID = FGuid::NewGuid();
	return Sticks;
}

void UItemDatabase::CreateItemDataTableAsset()
{
#if WITH_EDITOR
	UE_LOG(LogTemp, Warning, TEXT("ItemDatabase: Creating ItemDataTable asset..."));
	UE_LOG(LogTemp, Warning, TEXT("To create the DataTable:"));
	UE_LOG(LogTemp, Warning, TEXT("1. In Content Browser, create folder: Content/Data"));
	UE_LOG(LogTemp, Warning, TEXT("2. Right-click in Data folder -> Miscellaneous -> Data Table"));
	UE_LOG(LogTemp, Warning, TEXT("3. Choose Row Structure: ItemTableRow"));
	UE_LOG(LogTemp, Warning, TEXT("4. Name it: ItemDataTable"));
	UE_LOG(LogTemp, Warning, TEXT("5. Add rows for: wood, sticks, IronAxe, IronPickaxe, etc."));
#else
	UE_LOG(LogTemp, Warning, TEXT("ItemDatabase: CreateItemDataTableAsset only works in editor"));
#endif
}

ABlackTideWorldItem* UItemDatabase::SpawnWorldItem(UWorld* World, const FString& ItemID, const FVector& Location, int32 StackSize)
{
	if (!World)
	{
		UE_LOG(LogTemp, Warning, TEXT("SpawnWorldItem: No world provided"));
		return nullptr;
	}

	// Create the item
	FInventoryItem Item = CreateItem(ItemID, StackSize);
	if (!Item.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("SpawnWorldItem: Failed to create item %s"), *ItemID);
		return nullptr;
	}

	// Spawn the world item actor
	ABlackTideWorldItem* WorldItem = World->SpawnActor<ABlackTideWorldItem>(Location, FRotator::ZeroRotator);
	if (WorldItem)
	{
		// Get mesh from data table
		UStaticMesh* Mesh = GetItemWorldMesh(ItemID);
		WorldItem->SetupItem(Item, Mesh);

		UE_LOG(LogTemp, Log, TEXT("Spawned world item: %s x%d at %s"),
			*Item.Name, StackSize, *Location.ToString());
	}

	return WorldItem;
}

// Carry System Functions
bool UItemDatabase::CanItemBeCarried(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (!ItemDef)
	{
		return false;
	}

	return ItemDef->CarryType == EItemCarryType::CarryOnly || ItemDef->CarryType == EItemCarryType::Both;
}

bool UItemDatabase::CanItemGoInInventory(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (!ItemDef)
	{
		return false;
	}

	return ItemDef->CarryType == EItemCarryType::InventoryOnly || ItemDef->CarryType == EItemCarryType::Both;
}

EItemCarryType UItemDatabase::GetItemCarryType(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	return ItemDef ? ItemDef->CarryType : EItemCarryType::InventoryOnly;
}

EEquipmentSlot UItemDatabase::GetPreferredCarrySlot(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	return ItemDef ? ItemDef->PreferredCarrySlot : EEquipmentSlot::Shoulder;
}

UStaticMesh* UItemDatabase::GetItemCarryMesh(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (!ItemDef || ItemDef->CarryMesh.IsNull())
	{
		return nullptr;
	}

	return ItemDef->CarryMesh.LoadSynchronous();
}

float UItemDatabase::GetCarryMovementSpeedMultiplier(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	return ItemDef ? ItemDef->CarryMovementSpeedMultiplier : 1.0f;
}

bool UItemDatabase::CanItemBePlacedAsBuilding(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	return ItemDef ? ItemDef->bCanBePlacedAsBuilding : false;
}

void UItemDatabase::ClassifyExistingItems()
{
	UE_LOG(LogTemp, Warning, TEXT("=== CLASSIFYING EXISTING ITEMS FOR CARRY SYSTEM ==="));

	// This function helps classify existing items based on their names/types
	// You would run this once to set up your item database properly

	// Example classifications (you'll need to adjust based on your actual items):

	// CARRY-ONLY ITEMS (Large building materials)
	TArray<FString> CarryOnlyItems = {
		TEXT("log"),
		TEXT("wood_plank"),
		TEXT("large_stone"),
		TEXT("beam"),
		TEXT("barrel"),
		TEXT("chest"),
		TEXT("foundation_stone"),
		TEXT("wall_wood")
	};

	// INVENTORY-ONLY ITEMS (Small consumables, materials)
	TArray<FString> InventoryOnlyItems = {
		TEXT("berry"),
		TEXT("apple"),
		TEXT("coin"),
		TEXT("rope"),
		TEXT("nail"),
		TEXT("cloth"),
		TEXT("leather"),
		TEXT("iron_ingot"),
		TEXT("gold_coin")
	};

	// BOTH ITEMS (Tools, weapons, medium materials)
	TArray<FString> BothItems = {
		TEXT("axe"),
		TEXT("hammer"),
		TEXT("sword"),
		TEXT("bow"),
		TEXT("pickaxe"),
		TEXT("small_stone"),
		TEXT("wood"),
		TEXT("iron_ore")
	};

	// Log the classifications
	UE_LOG(LogTemp, Warning, TEXT("Carry-Only Items (%d):"), CarryOnlyItems.Num());
	for (const FString& ItemID : CarryOnlyItems)
	{
		UE_LOG(LogTemp, Warning, TEXT("  - %s"), *ItemID);
	}

	UE_LOG(LogTemp, Warning, TEXT("Inventory-Only Items (%d):"), InventoryOnlyItems.Num());
	for (const FString& ItemID : InventoryOnlyItems)
	{
		UE_LOG(LogTemp, Warning, TEXT("  - %s"), *ItemID);
	}

	UE_LOG(LogTemp, Warning, TEXT("Both Items (%d):"), BothItems.Num());
	for (const FString& ItemID : BothItems)
	{
		UE_LOG(LogTemp, Warning, TEXT("  - %s"), *ItemID);
	}

	UE_LOG(LogTemp, Warning, TEXT("=== ITEM CLASSIFICATION COMPLETE ==="));
	UE_LOG(LogTemp, Warning, TEXT("NOTE: You need to manually update your ItemDataTable with these CarryType values"));
}

UStaticMesh* UItemDatabase::GetItemWorldMesh(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (ItemDef && !ItemDef->WorldMesh.IsNull())
	{
		UStaticMesh* LoadedMesh = ItemDef->WorldMesh.LoadSynchronous();
		if (LoadedMesh)
		{
			UE_LOG(LogTemp, Log, TEXT("Loaded world mesh for %s"), *ItemID);
			return LoadedMesh;
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Failed to load world mesh for %s"), *ItemID);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("No world mesh defined for %s"), *ItemID);
	}
	return nullptr;
}

FVector UItemDatabase::GetItemWorldScale(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (ItemDef)
	{
		// Use scale from WorldMeshTransform
		FVector Scale = ItemDef->WorldMeshTransform.GetScale3D();
		if (Scale.IsZero())
		{
			Scale = FVector(1.0f, 1.0f, 1.0f);
		}
		UE_LOG(LogTemp, Log, TEXT("World scale for %s: %s"), *ItemID, *Scale.ToString());
		return Scale;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("No item definition found for %s, using default scale"), *ItemID);
		return FVector(1.0f, 1.0f, 1.0f);
	}
}

UAnimMontage* UItemDatabase::GetItemHoldingAnimation(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (ItemDef && !ItemDef->HoldingAnimation.IsNull())
	{
		return ItemDef->HoldingAnimation.LoadSynchronous();
	}
	return nullptr;
}

void UItemDatabase::ListAllItems()
{
	// Load the item data table
	static UDataTable* ItemDataTable = nullptr;
	if (!ItemDataTable)
	{
		ItemDataTable = LoadObject<UDataTable>(nullptr, TEXT("/Game/Data/ItemDataTable.ItemDataTable"));
		if (!ItemDataTable)
		{
			UE_LOG(LogTemp, Error, TEXT("ListAllItems: Could not load ItemDataTable"));
			return;
		}
	}

	// Get all row names
	TArray<FName> RowNames = ItemDataTable->GetRowNames();

	UE_LOG(LogTemp, Warning, TEXT("=== AVAILABLE ITEMS IN DATATABLE ==="));
	UE_LOG(LogTemp, Warning, TEXT("Total items: %d"), RowNames.Num());
	UE_LOG(LogTemp, Warning, TEXT("Usage: AddItem <ItemID> <Amount>"));
	UE_LOG(LogTemp, Warning, TEXT(""));

	for (const FName& RowName : RowNames)
	{
		FItemTableRow* ItemDef = ItemDataTable->FindRow<FItemTableRow>(RowName, TEXT("ListAllItems"));
		if (ItemDef)
		{
			UE_LOG(LogTemp, Warning, TEXT("- %s (%s) - %s"),
				*RowName.ToString(),
				*ItemDef->Name,
				*UEnum::GetValueAsString(ItemDef->ItemType));
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("=== END OF ITEM LIST ==="));
}

FInventoryItem UItemDatabase::CreatePlantFiber2(int32 StackSize)
{
	FInventoryItem Fiber;
	Fiber.ItemID = TEXT("fiber");
	Fiber.Name = TEXT("Plant Fiber");
	Fiber.Description = TEXT("Tough plant fibers. Essential for crafting ropes and cloth.");
	Fiber.ItemType = EItemType::Resource;
	Fiber.MaxStackSize = 100;
	Fiber.CurrentStackSize = FMath::Clamp(StackSize, 1, 100);
	Fiber.Value = 1;
	Fiber.Weight = 0.05f;

	Fiber.UniqueID = FGuid::NewGuid();
	return Fiber;
}

// NEW: Enhanced item system utility functions
bool UItemDatabase::CanPlayerUseItem(const FString& ItemID, int32 PlayerLevel, const TArray<FString>& PlayerSkills)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (!ItemDef)
	{
		return false;
	}

	// Check level requirement
	if (PlayerLevel < ItemDef->Requirements.MinLevel)
	{
		UE_LOG(LogTemp, Warning, TEXT("Player level %d too low for item %s (requires %d)"),
			PlayerLevel, *ItemID, ItemDef->Requirements.MinLevel);
		return false;
	}

	// Check skill requirements
	for (const FString& RequiredSkill : ItemDef->Requirements.RequiredSkills)
	{
		if (!PlayerSkills.Contains(RequiredSkill))
		{
			UE_LOG(LogTemp, Warning, TEXT("Player missing required skill %s for item %s"),
				*RequiredSkill, *ItemID);
			return false;
		}
	}

	return true;
}

float UItemDatabase::GetToolEfficiencyForResource(const FString& ToolItemID, const FString& ResourceType)
{
	FItemTableRow* ToolDef = GetItemDefinition(ToolItemID);
	if (!ToolDef || ToolDef->ItemType != EItemType::Tool)
	{
		return 1.0f; // Default efficiency
	}

	// Check if tool has specific efficiency for this resource type
	if (const float* Efficiency = ToolDef->ToolEfficiency.ResourceEfficiency.Find(ResourceType))
	{
		return *Efficiency * ToolDef->ToolEfficiency.BaseGatherSpeed;
	}

	// Return base gather speed if no specific efficiency
	return ToolDef->ToolEfficiency.BaseGatherSpeed;
}

FLinearColor UItemDatabase::GetRarityColor(EItemRarity Rarity)
{
	switch (Rarity)
	{
		case EItemRarity::Common:		return FLinearColor::White;
		case EItemRarity::Uncommon:		return FLinearColor::Green;
		case EItemRarity::Rare:			return FLinearColor::Blue;
		case EItemRarity::Epic:			return FLinearColor(0.5f, 0.0f, 1.0f); // Purple
		case EItemRarity::Legendary:	return FLinearColor(1.0f, 0.5f, 0.0f); // Orange
		case EItemRarity::Artifact:		return FLinearColor::Red;
		default:						return FLinearColor::White;
	}
}

TArray<FString> UItemDatabase::GetCraftingMaterialsForItem(const FString& ItemID)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (!ItemDef)
	{
		return TArray<FString>();
	}

	TArray<FString> Materials;
	for (const auto& Material : ItemDef->CraftingMaterials)
	{
		Materials.Add(FString::Printf(TEXT("%s x%d"), *Material.Key, Material.Value));
	}

	return Materials;
}

bool UItemDatabase::CanCraftItem(const FString& ItemID, const TMap<FString, int32>& AvailableMaterials)
{
	FItemTableRow* ItemDef = GetItemDefinition(ItemID);
	if (!ItemDef)
	{
		return false;
	}

	// Check if player has all required materials
	for (const auto& RequiredMaterial : ItemDef->CraftingMaterials)
	{
		const FString& MaterialID = RequiredMaterial.Key;
		int32 RequiredAmount = RequiredMaterial.Value;

		const int32* AvailableAmount = AvailableMaterials.Find(MaterialID);
		if (!AvailableAmount || *AvailableAmount < RequiredAmount)
		{
			UE_LOG(LogTemp, Warning, TEXT("Insufficient materials for %s: need %d %s, have %d"),
				*ItemID, RequiredAmount, *MaterialID, AvailableAmount ? *AvailableAmount : 0);
			return false;
		}
	}

	return true;
}
