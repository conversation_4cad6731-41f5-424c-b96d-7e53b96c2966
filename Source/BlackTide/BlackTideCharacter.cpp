// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideCharacter.h"
#include "SurvivalComponent.h"
#include "Engine/LocalPlayer.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/Controller.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "Engine/Engine.h"
#include "InventoryComponent.h"
#include "ItemDatabase.h"
#include "BlackTideHUD.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Components/BlackTideGatheringComponent.h"
#include "CraftingPanelWidget.h"
#include "Components/BlackTideCraftingComponent.h"
#include "Components/BlackTideEquipmentComponent.h"
#include "Components/BlackTideAnimationComponent.h"
#include "Components/BlackTideWeaponHitComponent.h"
#include "Components/BlackTideInteractableComponent.h"
#include "Weapons/BlackTideWeaponBase.h"
#include "Actors/BlackTideResourceNode.h"
#include "Actors/BlackTideCraftingStation.h"
#include "Actors/BlackTideWorldItem.h"
#include "Engine/OverlapResult.h"
#include "CraftingRecipeDatabase.h"

DEFINE_LOG_CATEGORY(LogTemplateCharacter);

//////////////////////////////////////////////////////////////////////////
// ABlackTideCharacter

ABlackTideCharacter::ABlackTideCharacter()
{
	// Set size for collision capsule
	GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);
		
	// Don't rotate when the controller rotates. Let that just affect the camera.
	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;

	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = true; // Character moves in the direction of input...	
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f); // ...at this rotation rate

	// Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
	// instead of recompiling to adjust them
	GetCharacterMovement()->JumpZVelocity = 700.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
	GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

	// Create a camera boom (pulls in towards the player if there is a collision)
	CameraBoom = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraBoom"));
	CameraBoom->SetupAttachment(RootComponent);
	CameraBoom->TargetArmLength = 400.0f; // The camera follows at this distance behind the character	
	CameraBoom->bUsePawnControlRotation = true; // Rotate the arm based on the controller

	// Create a follow camera
	FollowCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FollowCamera"));
	FollowCamera->SetupAttachment(CameraBoom, USpringArmComponent::SocketName); // Attach the camera to the end of the boom and let the boom adjust to match the controller orientation
	FollowCamera->bUsePawnControlRotation = false; // Camera does not rotate relative to arm

	// Create survival component
	SurvivalComponent = CreateDefaultSubobject<USurvivalComponent>(TEXT("SurvivalComponent"));

	// Create inventory component
	InventoryComponent = CreateDefaultSubobject<UInventoryComponent>(TEXT("InventoryComponent"));

	// Create gathering component
	GatheringComponent = CreateDefaultSubobject<UBlackTideGatheringComponent>(TEXT("GatheringComponent"));

	// Create crafting component
	CraftingComponent = CreateDefaultSubobject<UBlackTideCraftingComponent>(TEXT("CraftingComponent"));

	// Create equipment component
	EquipmentComponent = CreateDefaultSubobject<UBlackTideEquipmentComponent>(TEXT("EquipmentComponent"));

	// Create animation component
	AnimationComponent = CreateDefaultSubobject<UBlackTideAnimationComponent>(TEXT("AnimationComponent"));

	// Create weapon hit component
	WeaponHitComponent = CreateDefaultSubobject<UBlackTideWeaponHitComponent>(TEXT("WeaponHitComponent"));

	// Initialize UI references
	CraftingPanelWidget = nullptr;

	// Debug: Verify component creation
	if (SurvivalComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("CONSTRUCTOR: SurvivalComponent created successfully"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("CONSTRUCTOR: Failed to create SurvivalComponent!"));
	}

	if (InventoryComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("CONSTRUCTOR: InventoryComponent created successfully"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("CONSTRUCTOR: Failed to create InventoryComponent"));
	}

	// Set slower default movement speeds
	GetCharacterMovement()->MaxWalkSpeed = 150.0f; // Slower walking speed

	// Initialize survival state tracking
	BaseWalkSpeed = GetCharacterMovement()->MaxWalkSpeed;
	BaseRunSpeed = 300.0f; // Medium run speed (between walk and sprint)
	bSurvivalEffectsApplied = false;

	// Initialize jump settings
	JumpAnimationDelay = 0.5f; // 0.5 seconds delay (30 frames at 60fps)
	bWantsToJump = false;
	bIsPreJumping = false;
	bIsPlayingJumpMontage = false;

	// Initialize sprint settings
	SprintSpeed = 800.0f; // Sprint speed (faster than base walk speed)
	SprintStaminaDrainRate = 3.0f; // Stamina points per second while sprinting (reduced from 5.0 for better balance)
	MinStaminaToSprint = 10.0f; // Minimum stamina required to start sprinting (reduced from 15 for smoother gameplay)
	bRequireMovementToSprint = true; // Must be moving to sprint
	bIsSprinting = false;



	// Note: The skeletal mesh and anim blueprint references on the Mesh component (inherited from Character)
	// are set in the derived blueprint asset named BP_BlackTideCharacter (to avoid direct content references in C++)
}

void ABlackTideCharacter::BeginPlay()
{
	Super::BeginPlay();

	// Initialize survival effects
	if (SurvivalComponent)
	{
		// Bind to survival events
		SurvivalComponent->OnSurvivalEffectChanged.AddDynamic(this, &ABlackTideCharacter::OnSurvivalEffectChanged);

		UE_LOG(LogTemp, Log, TEXT("BlackTideCharacter: SurvivalComponent initialized for %s"),
			*GetName());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("BlackTideCharacter: SurvivalComponent is null!"));
	}

	// Ensure inventory is initialized
	if (InventoryComponent && !InventoryComponent->IsInventoryInitialized())
	{
		UE_LOG(LogTemp, Warning, TEXT("BlackTideCharacter: Inventory not initialized, forcing initialization"));
		InventoryComponent->ForceInitializeInventory();
	}
}

void ABlackTideCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Removed problematic safety check - Landed() function handles cleanup properly
	// The safety check was incorrectly stopping valid jump attempts

	// Survival effects are now handled by events, no need to check every tick
}

void ABlackTideCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABlackTideCharacter, bIsSprinting);
}

void ABlackTideCharacter::Landed(const FHitResult& Hit)
{
	Super::Landed(Hit);

	UE_LOG(LogTemp, Log, TEXT("Character landed - stopping jump montage"));

	// Stop jump montage and reset jump states
	StopJumpMontage();
	HandleJumpLanding();
}

//////////////////////////////////////////////////////////////////////////
// Survival Functions

float ABlackTideCharacter::GetSurvivalMovementMultiplier() const
{
	if (SurvivalComponent)
	{
		return SurvivalComponent->GetMovementSpeedMultiplier();
	}
	return 1.0f;
}

void ABlackTideCharacter::ApplySurvivalEffectsToMovement()
{
	if (!SurvivalComponent) return;

	UCharacterMovementComponent* MovementComp = GetCharacterMovement();
	if (!MovementComp) return;

	float SpeedMultiplier = SurvivalComponent->GetMovementSpeedMultiplier();
	bool bHasEffects = SpeedMultiplier < 1.0f;

	// Only apply and log if there's a change in state
	if (bHasEffects != bSurvivalEffectsApplied)
	{
		// Apply speed multiplier to movement
		float NewWalkSpeed = BaseWalkSpeed * SpeedMultiplier;
		float NewRunSpeed = BaseRunSpeed * SpeedMultiplier;

		MovementComp->MaxWalkSpeed = NewWalkSpeed;

		// Log the change
		if (bHasEffects)
		{
			UE_LOG(LogTemp, Warning, TEXT("Survival effects applied: Movement speed reduced to %.1f%% (Walk: %.1f)"),
				SpeedMultiplier * 100.0f, NewWalkSpeed);
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Survival effects normal: Full movement speed restored (%.1f)"), NewWalkSpeed);
		}

		bSurvivalEffectsApplied = bHasEffects;
	}
}

void ABlackTideCharacter::OnSurvivalEffectChanged(bool bHasNegativeEffects)
{
	// Called when survival effects change
	ApplySurvivalEffectsToMovement();

	if (bHasNegativeEffects)
	{
		UE_LOG(LogTemp, Warning, TEXT("Character %s now has negative survival effects"), *GetName());
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("Character %s survival effects normalized"), *GetName());
	}
}

//////////////////////////////////////////////////////////////////////////
// Sprint System

void ABlackTideCharacter::StartSprint()
{
	// Client calls server
	if (GetLocalRole() != ROLE_Authority)
	{
		ServerStartSprint();
		// Also update locally for immediate feedback
		bIsSprinting = true;
		UpdateMovementSpeed();
		return;
	}

	// Server execution
	if (!CanSprint())
	{
		UE_LOG(LogTemp, Log, TEXT("Cannot sprint - conditions not met"));
		return;
	}

	bIsSprinting = true;

	// Update movement speed
	UpdateMovementSpeed();

	// Only server handles stamina drain
	if (SurvivalComponent && SprintStaminaDrainRate > 0.0f)
	{
		GetWorldTimerManager().SetTimer(SprintStaminaDrainTimer, [this]()
		{
			if (bIsSprinting && SurvivalComponent)
			{
				float CurrentStamina = SurvivalComponent->GetCurrentStamina();

				// Drain stamina (server only)
				SurvivalComponent->ModifyStamina(-SprintStaminaDrainRate);

				// Stop sprinting if stamina too low, with small buffer to prevent flickering
				if (CurrentStamina <= MinStaminaToSprint)
				{
					StopSprint();
					UE_LOG(LogTemp, Log, TEXT("Sprint stopped - insufficient stamina (%.1f/%.1f)"),
						CurrentStamina, MinStaminaToSprint);
				}
			}
		}, 1.0f, true); // Drain every second
	}

	UE_LOG(LogTemp, Log, TEXT("Sprint started - Speed: %.1f (Role: %s)"),
		SprintSpeed, GetLocalRole() == ROLE_Authority ? TEXT("Server") : TEXT("Client"));
}

void ABlackTideCharacter::StopSprint()
{
	// Client calls server
	if (GetLocalRole() != ROLE_Authority)
	{
		ServerStopSprint();
		// Also update locally for immediate feedback
		bIsSprinting = false;
		UpdateMovementSpeed();
		return;
	}

	// Server execution
	if (!bIsSprinting) return;

	bIsSprinting = false;

	// Update movement speed (will handle auto-run if active)
	UpdateMovementSpeed();

	// Stop stamina drain timer
	GetWorldTimerManager().ClearTimer(SprintStaminaDrainTimer);

	UE_LOG(LogTemp, Log, TEXT("Sprint stopped - Speed restored to: %.1f"), GetCharacterMovement()->MaxWalkSpeed);
}

bool ABlackTideCharacter::CanSprint() const
{
	// Check if already sprinting (allow continuation)
	if (bIsSprinting) return true;

	// Check if in air
	if (GetCharacterMovement()->IsFalling()) return false;

	// Check stamina requirement with small buffer for starting sprint
	if (SurvivalComponent)
	{
		float CurrentStamina = SurvivalComponent->GetCurrentStamina();
		float RequiredStamina = bIsSprinting ? 0.0f : MinStaminaToSprint; // No stamina needed to continue, but need minimum to start

		if (CurrentStamina < RequiredStamina)
		{
			return false;
		}
	}

	// Check movement requirement
	if (bRequireMovementToSprint)
	{
		FVector Velocity = GetVelocity();
		if (Velocity.Size() < 50.0f) // Must be moving at least 50 units/sec
		{
			return false;
		}
	}

	return true;
}

void ABlackTideCharacter::UpdateMovementSpeed()
{
	if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
	{
		float TargetSpeed = BaseWalkSpeed; // Default walk speed

		if (bIsSprinting)
		{
			TargetSpeed = SprintSpeed; // Sprint speed
		}

		// Apply survival effects
		if (SurvivalComponent)
		{
			TargetSpeed *= SurvivalComponent->GetMovementSpeedMultiplier();
		}

		MovementComp->MaxWalkSpeed = TargetSpeed;

		UE_LOG(LogTemp, Log, TEXT("Movement speed updated: %.1f (Sprint: %s)"),
			TargetSpeed, bIsSprinting ? TEXT("Yes") : TEXT("No"));
	}
}

void ABlackTideCharacter::HandleJumpLanding()
{
	// Reset jump-related flags
	bWantsToJump = false;
	bIsPreJumping = false;
	bIsPlayingJumpMontage = false;

	UE_LOG(LogTemp, Warning, TEXT("DEBUG: HandleJumpLanding - All jump states reset, ready for next jump"));
}

void ABlackTideCharacter::StopJumpMontage()
{
	if (bIsPlayingJumpMontage && GetMesh()->GetAnimInstance())
	{
		// Stop the jump montage
		if (JumpMontage)
		{
			GetMesh()->GetAnimInstance()->Montage_Stop(0.2f, JumpMontage);
			UE_LOG(LogTemp, Log, TEXT("Jump montage stopped"));
		}

		bIsPlayingJumpMontage = false;
	}
}

//////////////////////////////////////////////////////////////////////////
// Jump with Animation Sync

void ABlackTideCharacter::Jump()
{
	UE_LOG(LogTemp, Warning, TEXT("DEBUG: Jump() called"));

	// Don't jump if already in air or already playing jump montage
	if (GetCharacterMovement()->IsFalling() || bIsPlayingJumpMontage)
	{
		UE_LOG(LogTemp, Warning, TEXT("DEBUG: Jump blocked - InAir: %s, PlayingMontage: %s"),
			GetCharacterMovement()->IsFalling() ? TEXT("true") : TEXT("false"),
			bIsPlayingJumpMontage ? TEXT("true") : TEXT("false"));
		return;
	}

	// Multicast jump animation to all clients
	if (GetLocalRole() == ROLE_Authority)
	{
		UE_LOG(LogTemp, Warning, TEXT("DEBUG: Server multicasting jump animation"));
		MulticastPlayJumpAnimation();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("DEBUG: Client playing local jump animation"));
		// Local execution for immediate feedback
		PlayJumpAnimationLocal();
	}
}

void ABlackTideCharacter::PlayJumpAnimationLocal()
{
	// Use ONLY the montage system - no immediate jump
	if (JumpMontage && GetMesh()->GetAnimInstance())
	{
		// Play montage - this will trigger physical jump at frame 30 via Animation Notify
		float MontageLength = GetMesh()->GetAnimInstance()->Montage_Play(JumpMontage);
		bIsPlayingJumpMontage = true;
		bWantsToJump = true; // For animation state machine

		UE_LOG(LogTemp, Warning, TEXT("DEBUG: Jump montage started! Physical jump will occur at frame 30. Length: %.2f seconds"), MontageLength);
	}
	else
	{
		// Fallback: No montage available, do immediate jump
		UE_LOG(LogTemp, Error, TEXT("DEBUG: No jump montage available - doing immediate jump as fallback"));
		Super::Jump();
		bWantsToJump = true;
	}
}

void ABlackTideCharacter::ExecuteDelayedJump()
{
	UE_LOG(LogTemp, Warning, TEXT("DEBUG: ExecuteDelayedJump() called - This should ONLY be called by Animation Notify"));

	// This function should ONLY be called by the Animation Notify at frame 30
	// Execute the physical jump (this is the ONLY physical jump execution)
	Super::Jump();

	UE_LOG(LogTemp, Warning, TEXT("DEBUG: Physical jump executed at frame 30!"));
}

//////////////////////////////////////////////////////////////////////////
// Input

void ABlackTideCharacter::NotifyControllerChanged()
{
	Super::NotifyControllerChanged();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}
}

void ABlackTideCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	UE_LOG(LogTemp, Warning, TEXT("🎮 SetupPlayerInputComponent called for %s"), *GetName());

	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent)) {

		UE_LOG(LogTemp, Warning, TEXT("✅ Enhanced Input Component found, binding actions..."));

		// Validate Input Actions before binding
		UE_LOG(LogTemp, Warning, TEXT("🔍 Validating Input Actions:"));
		UE_LOG(LogTemp, Warning, TEXT("  JumpAction: %s"), JumpAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  MoveAction: %s"), MoveAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  LookAction: %s"), LookAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  SprintAction: %s"), SprintAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  InventoryAction: %s"), InventoryAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  InteractAction: %s"), InteractAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  ToggleWeaponAction: %s"), ToggleWeaponAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  MeleeAttackAction: %s"), MeleeAttackAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));
		UE_LOG(LogTemp, Warning, TEXT("  CraftingAction: %s"), CraftingAction ? TEXT("✅ Valid") : TEXT("❌ NULL"));

		// Jumping
		if (JumpAction)
		{
			EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Started, this, &ACharacter::Jump);
			EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Completed, this, &ACharacter::StopJumping);
		}

		// Sprinting
		if (SprintAction)
		{
			EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Started, this, &ABlackTideCharacter::StartSprint);
			EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Completed, this, &ABlackTideCharacter::StopSprint);
		}

		// Moving
		if (MoveAction)
		{
			EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ABlackTideCharacter::Move);
		}

		// Looking
		if (LookAction)
		{
			EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ABlackTideCharacter::Look);
		}

		// Inventory
		if (InventoryAction)
		{
			EnhancedInputComponent->BindAction(InventoryAction, ETriggerEvent::Triggered, this, &ABlackTideCharacter::OpenInventory);
		}

		// Interact
		if (InteractAction)
		{
			EnhancedInputComponent->BindAction(InteractAction, ETriggerEvent::Started, this, &ABlackTideCharacter::Interact);
		}

		// Toggle Weapon
		if (ToggleWeaponAction)
		{
			UE_LOG(LogTemp, Warning, TEXT("🔧 Binding ToggleWeaponAction to ToggleWeapon function"));
			EnhancedInputComponent->BindAction(ToggleWeaponAction, ETriggerEvent::Started, this, &ABlackTideCharacter::ToggleWeapon);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("❌ ToggleWeaponAction is NULL! Z key will not work!"));
		}

		// Melee Attack
		if (MeleeAttackAction)
		{
			UE_LOG(LogTemp, Warning, TEXT("🔧 Binding MeleeAttackAction to PerformMeleeAttack function"));
			EnhancedInputComponent->BindAction(MeleeAttackAction, ETriggerEvent::Started, this, &ABlackTideCharacter::PerformMeleeAttack);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("❌ MeleeAttackAction is NULL! Left mouse will not work!"));
		}

		// Crafting
		if (CraftingAction)
		{
			UE_LOG(LogTemp, Warning, TEXT("🔧 Binding CraftingAction to OpenCraftingUI function"));
			EnhancedInputComponent->BindAction(CraftingAction, ETriggerEvent::Started, this, &ABlackTideCharacter::OpenCraftingUI);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("❌ CraftingAction is NULL! Crafting key will not work!"));
		}

		UE_LOG(LogTemp, Warning, TEXT("✅ Input binding setup complete"));
	}
	else
	{
		UE_LOG(LogTemplateCharacter, Error, TEXT("'%s' Failed to find an Enhanced Input component! This template is built to use the Enhanced Input system. If you intend to use the legacy system, then you will need to update this C++ file."), *GetNameSafe(this));
	}
}

void ABlackTideCharacter::Move(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D MovementVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);

		// get right vector
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

		// add movement
		AddMovementInput(ForwardDirection, MovementVector.Y);
		AddMovementInput(RightDirection, MovementVector.X);
	}
}

void ABlackTideCharacter::Look(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D LookAxisVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// add yaw and pitch input to controller
		AddControllerYawInput(LookAxisVector.X);
		AddControllerPitchInput(LookAxisVector.Y);
	}
}

void ABlackTideCharacter::OpenInventory(const FInputActionValue& Value)
{
	// Input debouncing - prevent rapid toggling
	float CurrentTime = GetWorld()->GetTimeSeconds();
	if (CurrentTime - LastInventoryToggleTime < InventoryToggleCooldown)
	{
		return;
	}

	LastInventoryToggleTime = CurrentTime;

	// Toggle inventory UI when I key is pressed
	if (APlayerController* PC = Cast<APlayerController>(GetController()))
	{
		if (ABlackTideHUD* HUD = Cast<ABlackTideHUD>(PC->GetHUD()))
		{
			HUD->ToggleInventoryUI();
			UE_LOG(LogTemp, Log, TEXT("BlackTideCharacter: Inventory toggled via I key"));
		}
	}
}

void ABlackTideCharacter::Interact(const FInputActionValue& Value)
{
	UE_LOG(LogTemp, Warning, TEXT("=== INTERACT PRESSED ==="));

	// FIRST: Check if we're carrying something and can drop it
	if (EquipmentComponent && EquipmentComponent->IsSlotEquipped(EEquipmentSlot::Shoulder))
	{
		UE_LOG(LogTemp, Warning, TEXT("🎒 Player has item on shoulder, attempting to drop..."));
		DropCarriedItem();
		return; // Early exit after dropping
	}

	// SECOND: Check for gathering targets (resource nodes)
	if (GatheringComponent)
	{
		UE_LOG(LogTemp, Log, TEXT("GatheringComponent found, checking for targets..."));

		// Toggle gathering - start if not gathering, stop if already gathering
		if (GatheringComponent->IsGathering())
		{
			GatheringComponent->StopGathering();
			UE_LOG(LogTemp, Log, TEXT("Stopped gathering"));
			return; // Early exit if we stopped gathering
		}

		// Try to start gathering from interactable
		bool bStarted = GatheringComponent->StartGathering();
		if (bStarted)
		{
			UE_LOG(LogTemp, Log, TEXT("✅ Started gathering from interactable"));
			return; // Success, we're done
		}
	}

	// THIRD: Check for world items to pickup
	UE_LOG(LogTemp, Warning, TEXT("🔍 No interactable found, searching for world items to pickup..."));
	ABlackTideWorldItem* NearestWorldItem = FindNearestWorldItem();
	if (NearestWorldItem)
	{
		UE_LOG(LogTemp, Warning, TEXT("✅ Found world item: %s"), *NearestWorldItem->GetName());

		// Trigger pickup based on carry type
		FInventoryItem ItemData = NearestWorldItem->GetItemData();
		if (ItemData.IsValid())
		{
			EItemCarryType CarryType = UItemDatabase::GetItemCarryType(ItemData.ItemID);

			switch (CarryType)
			{
				case EItemCarryType::InventoryOnly:
					UE_LOG(LogTemp, Warning, TEXT("📦 Picking up to inventory: %s"), *ItemData.Name);
					NearestWorldItem->PickupToInventory(this);
					break;

				case EItemCarryType::CarryOnly:
					// Check if shoulder slot is available
					if (EquipmentComponent && !EquipmentComponent->IsSlotEquipped(EEquipmentSlot::Shoulder))
					{
						UE_LOG(LogTemp, Warning, TEXT("🎒 Picking up to carry: %s"), *ItemData.Name);
						NearestWorldItem->PickupToCarry(this);
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("❌ Cannot carry %s - shoulder slot occupied"), *ItemData.Name);
					}
					break;

				case EItemCarryType::Both:
					UE_LOG(LogTemp, Warning, TEXT("📦 Picking up to inventory (default): %s"), *ItemData.Name);
					NearestWorldItem->PickupToInventory(this);
					break;
			}
			return; // Success
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("❌ No valid interaction targets found"));
}

ABlackTideWorldItem* ABlackTideCharacter::FindNearestWorldItem() const
{
	if (!GetWorld())
	{
		return nullptr;
	}

	// Search for world items in a sphere around the character
	FVector CharacterLocation = GetActorLocation();
	float SearchRadius = 300.0f; // 3 meter radius

	// Use sphere overlap to find world items
	TArray<FOverlapResult> OverlapResults;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(this);

	bool bFoundOverlaps = GetWorld()->OverlapMultiByObjectType(
		OverlapResults,
		CharacterLocation,
		FQuat::Identity,
		FCollisionObjectQueryParams(ECollisionChannel::ECC_WorldDynamic),
		FCollisionShape::MakeSphere(SearchRadius),
		QueryParams
	);

	if (!bFoundOverlaps)
	{
		UE_LOG(LogTemp, Log, TEXT("🔍 No world items found in sphere overlap"));
		return nullptr;
	}

	// Find the nearest world item
	ABlackTideWorldItem* NearestWorldItem = nullptr;
	float NearestDistance = FLT_MAX;

	for (const FOverlapResult& Result : OverlapResults)
	{
		ABlackTideWorldItem* WorldItem = Cast<ABlackTideWorldItem>(Result.GetActor());
		if (WorldItem && WorldItem->CanBePickedUp() && WorldItem->GetItemData().IsValid())
		{
			float Distance = FVector::Dist(CharacterLocation, WorldItem->GetActorLocation());
			if (Distance < NearestDistance)
			{
				NearestDistance = Distance;
				NearestWorldItem = WorldItem;
			}
		}
	}

	if (NearestWorldItem)
	{
		UE_LOG(LogTemp, Log, TEXT("🎯 Found nearest world item: %s at distance %.1f"),
			*NearestWorldItem->GetName(), NearestDistance);
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("🔍 No valid world items found in range"));
	}

	return NearestWorldItem;
}

void ABlackTideCharacter::DropCarriedItem()
{
	if (!EquipmentComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("❌ No equipment component for dropping"));
		return;
	}

	// Check if shoulder slot has an item
	if (!EquipmentComponent->IsSlotEquipped(EEquipmentSlot::Shoulder))
	{
		UE_LOG(LogTemp, Warning, TEXT("❌ No item on shoulder to drop"));
		return;
	}

	// Get the carried item
	FInventoryItem CarriedItem = EquipmentComponent->GetEquippedItem(EEquipmentSlot::Shoulder);
	if (!CarriedItem.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("❌ Invalid carried item"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("🎒 Dropping carried item: %s"), *CarriedItem.Name);

	// Calculate drop location (in front of player)
	FVector DropLocation = GetActorLocation() + GetActorForwardVector() * 150.0f;
	DropLocation.Z = GetActorLocation().Z; // Keep same height

	// Spawn world item at drop location
	ABlackTideWorldItem* DroppedItem = UItemDatabase::SpawnWorldItem(GetWorld(), CarriedItem.ItemID, DropLocation, CarriedItem.CurrentStackSize);
	if (DroppedItem)
	{
		UE_LOG(LogTemp, Warning, TEXT("✅ Successfully dropped %s at location"), *CarriedItem.Name);

		// Remove item from shoulder slot
		EquipmentComponent->UnequipItem(EEquipmentSlot::Shoulder);

		UE_LOG(LogTemp, Warning, TEXT("✅ Removed %s from shoulder slot"), *CarriedItem.Name);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("❌ Failed to spawn dropped item %s"), *CarriedItem.Name);
	}
}

void ABlackTideCharacter::ToggleWeapon(const FInputActionValue& Value)
{
	UE_LOG(LogTemp, Warning, TEXT("🎮 ToggleWeapon INPUT TRIGGERED! Z key pressed"));

	// Toggle weapon position between hand and back
	if (EquipmentComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("🔧 EquipmentComponent found, calling ToggleWeaponPosition"));
		EquipmentComponent->ToggleWeaponPosition();
		UE_LOG(LogTemp, Log, TEXT("BlackTideCharacter: Weapon position toggled via Z key"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("❌ BlackTideCharacter: No EquipmentComponent found for weapon toggle"));
	}
}

void ABlackTideCharacter::PerformMeleeAttack(const FInputActionValue& Value)
{
	UE_LOG(LogTemp, Warning, TEXT("🎮 PerformMeleeAttack INPUT TRIGGERED! Left mouse pressed"));

	// Check attack cooldown
	float CurrentTime = GetWorld()->GetTimeSeconds();
	if (CurrentTime - LastAttackTime < AttackCooldown)
	{
		UE_LOG(LogTemp, Warning, TEXT("⏰ Attack on cooldown! %.2f seconds remaining"),
			AttackCooldown - (CurrentTime - LastAttackTime));
		return;
	}

	// Update last attack time
	LastAttackTime = CurrentTime;
	UE_LOG(LogTemp, Warning, TEXT("✅ Attack cooldown passed, proceeding with attack"));

	// Get equipped weapon for attack
	if (EquipmentComponent)
	{
		// Try to get weapon actor first (advanced weapons)
		ABlackTideWeaponBase* EquippedWeapon = EquipmentComponent->GetEquippedWeapon(EEquipmentSlot::MainHand);
		if (EquippedWeapon)
		{
			UE_LOG(LogTemp, Warning, TEXT("🗡️ Found equipped weapon actor: %s"), *EquippedWeapon->WeaponName);

			// Start weapon attack (handles collision, animation, etc.)
			EquippedWeapon->StartAttack(this);

			// Multicast attack animation to all clients
			UAnimMontage* AttackMontage = EquippedWeapon->GetAttackMontage();
			if (AttackMontage)
			{
				float AttackSpeed = EquippedWeapon->GetAttackSpeed();

				if (GetLocalRole() == ROLE_Authority)
				{
					UE_LOG(LogTemp, Warning, TEXT("🌐 Server multicasting attack animation for weapon %s"), *EquippedWeapon->WeaponName);
					MulticastPlayAttackAnimation(AttackMontage, AttackSpeed);
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("🎮 Client playing local attack animation"));
					// Local execution for immediate feedback
					if (AnimationComponent)
					{
						bool bAnimationStarted = AnimationComponent->PlayMontage(AttackMontage, AttackSpeed);
						if (bAnimationStarted)
						{
							UE_LOG(LogTemp, Warning, TEXT("🎬 Local attack animation started for weapon %s"), *EquippedWeapon->WeaponName);
						}
					}
				}
			}
		}
		else
		{
			// Fallback to old system for simple equipment (static meshes)
			UE_LOG(LogTemp, Warning, TEXT("⚠️ No weapon actor found, checking for static mesh equipment"));

			UStaticMeshComponent* EquippedMesh = EquipmentComponent->GetEquipmentMeshComponent(EEquipmentSlot::MainHand);
			if (EquippedMesh)
			{
				UE_LOG(LogTemp, Warning, TEXT("📦 Found static mesh equipment, using legacy system"));
				// Could implement legacy collision system here if needed
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("❌ No weapon or tool equipped in MainHand slot"));
			}
		}
	}

	// Note: Resource gathering now happens via weapon collision hits
	// The WeaponHitComponent will automatically detect hits and trigger resource gathering
	UE_LOG(LogTemp, Warning, TEXT("🎯 Attack initiated - weapon collision will handle resource gathering"));
}

void ABlackTideCharacter::OpenCraftingUI(const FInputActionValue& Value)
{
	UE_LOG(LogTemp, Warning, TEXT("🔨 OpenCraftingUI called"));
	ToggleCrafting();
}

// Test commands for inventory
void ABlackTideCharacter::AddApple(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Apple = UItemDatabase::CreateApple(Amount);
		int32 SlotIndex;
		InventoryComponent->AddItem(Apple, SlotIndex);
	}
}

void ABlackTideCharacter::AddWood(int32 Amount)
{
	if (InventoryComponent)
	{
		// Use new DataTable system instead of hardcoded CreateWood()
		FInventoryItem Wood = UItemDatabase::CreateItem(TEXT("wood"), Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Wood, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddWood: %s - Added %d wood to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddStone(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Stone = UItemDatabase::CreateStone(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Stone, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddStone: %s - Added %d stone to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddIronAxe()
{
	if (InventoryComponent)
	{
		// Use new DataTable system instead of hardcoded CreateIronAxe()
		FInventoryItem IronAxe = UItemDatabase::CreateItem(TEXT("IronAxe"), 1);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(IronAxe, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddIronAxe: %s - Added iron axe to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), SlotIndex);
	}
}

void ABlackTideCharacter::EquipAxe()
{
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("EquipAxe: No inventory component"));
		return;
	}

	// Find axe in inventory
	for (int32 i = 0; i < InventoryComponent->GetTotalSlots(); i++)
	{
		FInventoryItem Item = InventoryComponent->GetItemAtSlot(i);
		if (Item.IsValid() && Item.ItemID == TEXT("IronAxe"))
		{
			bool bEquipped = InventoryComponent->EquipTool(i);
			UE_LOG(LogTemp, Warning, TEXT("EquipAxe: %s - Equipped axe from slot %d"),
				bEquipped ? TEXT("SUCCESS") : TEXT("FAILED"), i);
			return;
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("EquipAxe: No axe found in inventory"));
}

void ABlackTideCharacter::AddIronPickaxe()
{
	if (InventoryComponent)
	{
		// Use new DataTable system instead of hardcoded CreateIronPickaxe()
		FInventoryItem IronPickaxe = UItemDatabase::CreateItem(TEXT("IronPickaxe"), 1);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(IronPickaxe, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddIronPickaxe: %s - Added iron pickaxe to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), SlotIndex);
	}
}

void ABlackTideCharacter::EquipPickaxe()
{
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("EquipPickaxe: No inventory component"));
		return;
	}

	// Find pickaxe in inventory
	for (int32 i = 0; i < InventoryComponent->GetTotalSlots(); i++)
	{
		FInventoryItem Item = InventoryComponent->GetItemAtSlot(i);
		if (Item.IsValid() && Item.ItemID == TEXT("IronPickaxe"))
		{
			bool bEquipped = InventoryComponent->EquipTool(i);
			UE_LOG(LogTemp, Warning, TEXT("EquipPickaxe: %s - Equipped pickaxe from slot %d"),
				bEquipped ? TEXT("SUCCESS") : TEXT("FAILED"), i);
			return;
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("EquipPickaxe: No pickaxe found in inventory"));
}

void ABlackTideCharacter::AddItem(const FString& ItemID, int32 Amount)
{
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("AddItem: No inventory component"));
		return;
	}

	// Use DataTable system to create any item
	FInventoryItem Item = UItemDatabase::CreateItem(ItemID, Amount);

	if (!Item.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("AddItem: Failed to create item '%s' - not found in DataTable"), *ItemID);
		return;
	}

	int32 SlotIndex;
	bool bSuccess = InventoryComponent->AddItem(Item, SlotIndex);

	UE_LOG(LogTemp, Warning, TEXT("AddItem: %s - Added %d '%s' to slot %d"),
		bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, *Item.Name, SlotIndex);
}

void ABlackTideCharacter::ListItems()
{
	UItemDatabase::ListAllItems();
}

void ABlackTideCharacter::AddHealthPotion(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem HealthPotion = UItemDatabase::CreateHealthPotion(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(HealthPotion, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddHealthPotion: %s - Added %d health potions to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddWaterBottle(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem WaterBottle = UItemDatabase::CreateWaterBottle(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(WaterBottle, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddWaterBottle: %s - Added %d water bottles to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

// === FOOD & CONSUMABLES ===

void ABlackTideCharacter::AddCoconut(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Coconut = UItemDatabase::CreateCoconut(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Coconut, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddCoconut: %s - Added %d coconuts to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddJuccaRoot(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem JuccaRoot = UItemDatabase::CreateJuccaRoot(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(JuccaRoot, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddJuccaRoot: %s - Added %d jucca roots to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddCookedJucca(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem CookedJucca = UItemDatabase::CreateCookedJucca(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(CookedJucca, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddCookedJucca: %s - Added %d cooked jucca to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddBerries(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Berries = UItemDatabase::CreateBerries(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Berries, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddBerries: %s - Added %d berries to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddRawCrab(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem RawCrab = UItemDatabase::CreateRawCrab(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(RawCrab, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddRawCrab: %s - Added %d raw crabs to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddCookedCrab(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem CookedCrab = UItemDatabase::CreateCookedCrab(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(CookedCrab, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddCookedCrab: %s - Added %d cooked crabs to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddDirtyWater(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem DirtyWater = UItemDatabase::CreateDirtyWater(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(DirtyWater, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddDirtyWater: %s - Added %d dirty water to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddBoiledWater(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem BoiledWater = UItemDatabase::CreateBoiledWater(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(BoiledWater, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddBoiledWater: %s - Added %d boiled water to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddHerbs(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Herbs = UItemDatabase::CreateHerbs(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Herbs, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddHerbs: %s - Added %d herbs to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

// === RESOURCES ===

void ABlackTideCharacter::AddWoodLog(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem WoodLog = UItemDatabase::CreateWoodLog(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(WoodLog, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddWoodLog: %s - Added %d wood logs to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddWoodPlank(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem WoodPlank = UItemDatabase::CreateWoodPlank(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(WoodPlank, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddWoodPlank: %s - Added %d wood planks to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddStoneChunk(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem StoneChunk = UItemDatabase::CreateStoneChunk(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(StoneChunk, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddStoneChunk: %s - Added %d stone chunks to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddRope(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Rope = UItemDatabase::CreateRope(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Rope, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddRope: %s - Added %d ropes to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddCoconutHusk(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem CoconutHusk = UItemDatabase::CreateCoconutHusk(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(CoconutHusk, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddCoconutHusk: %s - Added %d coconut husks to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddPlantFiber(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem PlantFiber = UItemDatabase::CreatePlantFiber(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(PlantFiber, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddPlantFiber: %s - Added %d plant fibers to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddClay(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Clay = UItemDatabase::CreateClay(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Clay, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddClay: %s - Added %d clay to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddFlint(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Flint = UItemDatabase::CreateFlint(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(Flint, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddFlint: %s - Added %d flint to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::AddPalmLeaf(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem PalmLeaf = UItemDatabase::CreatePalmLeaf(Amount);
		int32 SlotIndex;
		bool bSuccess = InventoryComponent->AddItem(PalmLeaf, SlotIndex);

		UE_LOG(LogTemp, Warning, TEXT("AddPalmLeaf: %s - Added %d palm leaves to slot %d"),
			bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), Amount, SlotIndex);
	}
}

void ABlackTideCharacter::ClearInventory()
{
	if (InventoryComponent)
	{
		for (int32 i = 0; i < InventoryComponent->GetTotalSlots(); i++)
		{
			InventoryComponent->RemoveItem(i);
		}

		UE_LOG(LogTemp, Warning, TEXT("ClearInventory: Inventory cleared"));
	}
}

void ABlackTideCharacter::RefreshInventoryUI()
{
	if (APlayerController* PC = Cast<APlayerController>(GetController()))
	{
		if (ABlackTideHUD* HUD = Cast<ABlackTideHUD>(PC->GetHUD()))
		{
			// Force refresh of any open inventory UI
		}
	}
}

void ABlackTideCharacter::ValidateInventoryState()
{
	if (InventoryComponent)
	{
		InventoryComponent->ValidateInventoryState();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("ValidateInventoryState: No InventoryComponent found!"));
	}
}

void ABlackTideCharacter::DebugEventBindings()
{
	if (InventoryComponent)
	{
		InventoryComponent->DebugEventBindings();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("DebugEventBindings: No InventoryComponent found!"));
	}
}

void ABlackTideCharacter::ToggleInventory()
{
	if (APlayerController* PC = Cast<APlayerController>(GetController()))
	{
		if (ABlackTideHUD* HUD = Cast<ABlackTideHUD>(PC->GetHUD()))
		{
			HUD->ToggleInventoryUI();
		}
	}
}

void ABlackTideCharacter::ToggleCrafting()
{
	if (IsCraftingPanelOpen())
	{
		CloseCraftingPanel();
	}
	else
	{
		OpenCraftingPanel();
	}
}

void ABlackTideCharacter::OpenCraftingPanel()
{
	if (!CraftingPanelWidgetClass)
	{
		UE_LOG(LogTemp, Error, TEXT("BlackTideCharacter: CraftingPanelWidgetClass not set in Blueprint!"));
		return;
	}

	if (CraftingPanelWidget && CraftingPanelWidget->IsInViewport())
	{
		UE_LOG(LogTemp, Warning, TEXT("BlackTideCharacter: Crafting panel already open"));
		return;
	}

	// Create crafting panel widget
	CraftingPanelWidget = CreateWidget<UCraftingPanelWidget>(GetWorld(), CraftingPanelWidgetClass);
	if (CraftingPanelWidget)
	{
		// Initialize with this character
		CraftingPanelWidget->InitializeWithCharacter(this);

		// Add to viewport
		CraftingPanelWidget->AddToViewport();

		// Set input mode to UI
		if (APlayerController* PC = Cast<APlayerController>(GetController()))
		{
			FInputModeGameAndUI InputMode;
			InputMode.SetWidgetToFocus(CraftingPanelWidget->TakeWidget());
			InputMode.SetLockMouseToViewportBehavior(EMouseLockMode::DoNotLock);
			PC->SetInputMode(InputMode);
			PC->SetShowMouseCursor(true);
		}

		UE_LOG(LogTemp, Log, TEXT("BlackTideCharacter: Crafting panel opened"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("BlackTideCharacter: Failed to create crafting panel widget"));
	}
}

void ABlackTideCharacter::CloseCraftingPanel()
{
	if (CraftingPanelWidget && CraftingPanelWidget->IsInViewport())
	{
		CraftingPanelWidget->RemoveFromParent();
		CraftingPanelWidget = nullptr;

		// Set input mode back to game
		if (APlayerController* PC = Cast<APlayerController>(GetController()))
		{
			FInputModeGameOnly InputMode;
			PC->SetInputMode(InputMode);
			PC->SetShowMouseCursor(false);
		}

		UE_LOG(LogTemp, Log, TEXT("BlackTideCharacter: Crafting panel closed"));
	}
}

bool ABlackTideCharacter::IsCraftingPanelOpen() const
{
	return CraftingPanelWidget && CraftingPanelWidget->IsInViewport();
}

void ABlackTideCharacter::DebugInventory()
{
	UE_LOG(LogTemp, Warning, TEXT("=== INVENTORY DEBUG ==="));

	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("DebugInventory: No InventoryComponent!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("DebugInventory: Grid size: %dx%d"),
		InventoryComponent->GridWidth, InventoryComponent->GridHeight);
	UE_LOG(LogTemp, Warning, TEXT("DebugInventory: Total slots: %d"),
		InventoryComponent->InventorySlots.Num());

	// If no slots, try to initialize manually
	if (InventoryComponent->InventorySlots.Num() == 0)
	{
		UE_LOG(LogTemp, Error, TEXT("DebugInventory: No slots found! Attempting manual initialization..."));
		// Force initialization - this is a workaround
		int32 TotalSlots = InventoryComponent->GridWidth * InventoryComponent->GridHeight;
		InventoryComponent->InventorySlots.Empty();
		InventoryComponent->InventorySlots.Reserve(TotalSlots);

		for (int32 Y = 0; Y < InventoryComponent->GridHeight; Y++)
		{
			for (int32 X = 0; X < InventoryComponent->GridWidth; X++)
			{
				FInventorySlot NewSlot(X, Y);
				InventoryComponent->InventorySlots.Add(NewSlot);
			}
		}

		UE_LOG(LogTemp, Warning, TEXT("DebugInventory: Manual initialization complete - now have %d slots"),
			InventoryComponent->InventorySlots.Num());
	}

	UE_LOG(LogTemp, Warning, TEXT("DebugInventory: Current weight: %.2f / %.2f"),
		InventoryComponent->GetCurrentWeight(), InventoryComponent->MaxWeight);

	// Count different slot states
	int32 EmptySlots = 0;
	int32 OccupiedSlots = 0;
	int32 InvalidSlots = 0;

	for (int32 i = 0; i < InventoryComponent->InventorySlots.Num(); i++)
	{
		const FInventorySlot& Slot = InventoryComponent->InventorySlots[i];

		if (Slot.IsEmpty())
		{
			EmptySlots++;
			// Log first few and last few empty slots for debugging
			if (i < 3 || i >= InventoryComponent->InventorySlots.Num() - 3)
			{
				UE_LOG(LogTemp, Log, TEXT("DebugInventory: Slot %d: EMPTY at grid (%d,%d)"), i, Slot.GridX, Slot.GridY);
			}
		}
		else if (Slot.Item.IsValid())
		{
			OccupiedSlots++;
			UE_LOG(LogTemp, Warning, TEXT("DebugInventory: Slot %d: '%s' (x%d) at grid (%d,%d)"),
				i, *Slot.Item.Name, Slot.Item.CurrentStackSize, Slot.GridX, Slot.GridY);
		}
		else
		{
			InvalidSlots++;
			UE_LOG(LogTemp, Error, TEXT("DebugInventory: Slot %d: INVALID STATE - Occupied but invalid item"), i);
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("DebugInventory: Slot Summary: %d Empty, %d Occupied, %d Invalid"),
		EmptySlots, OccupiedSlots, InvalidSlots);

	// Test FindEmptySlotForItem function
	FInventoryItem TestItem = UItemDatabase::CreateApple(1);
	int32 NextEmptySlot = InventoryComponent->FindEmptySlotForItem(TestItem);
	UE_LOG(LogTemp, Warning, TEXT("DebugInventory: FindEmptySlotForItem would return slot: %d"), NextEmptySlot);

	UE_LOG(LogTemp, Warning, TEXT("=== END DEBUG ==="));
}

void ABlackTideCharacter::InitInventory()
{
	UE_LOG(LogTemp, Warning, TEXT("InitInventory: Manual inventory initialization"));

	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("InitInventory: No InventoryComponent!"));
		return;
	}

	// Force initialization
	int32 TotalSlots = InventoryComponent->GridWidth * InventoryComponent->GridHeight;
	UE_LOG(LogTemp, Warning, TEXT("InitInventory: Creating %d slots (%dx%d)"),
		TotalSlots, InventoryComponent->GridWidth, InventoryComponent->GridHeight);

	InventoryComponent->InventorySlots.Empty();
	InventoryComponent->InventorySlots.Reserve(TotalSlots);

	for (int32 Y = 0; Y < InventoryComponent->GridHeight; Y++)
	{
		for (int32 X = 0; X < InventoryComponent->GridWidth; X++)
		{
			FInventorySlot NewSlot(X, Y);
			InventoryComponent->InventorySlots.Add(NewSlot);
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("InitInventory: ✅ Manual initialization complete - %d slots created"),
		InventoryComponent->InventorySlots.Num());
}

void ABlackTideCharacter::TestInventorySlots()
{
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("TestInventorySlots: No InventoryComponent!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("=== TESTING INVENTORY SLOTS ==="));

	// Clear inventory first using the public RemoveItem method
	for (int32 i = 0; i < InventoryComponent->InventorySlots.Num(); i++)
	{
		if (!InventoryComponent->IsSlotEmpty(i))
		{
			InventoryComponent->RemoveItem(i, -1); // -1 removes all items in the slot
		}
	}
	UE_LOG(LogTemp, Warning, TEXT("TestInventorySlots: Cleared all slots"));

	// Test adding items to specific slots to verify the system works
	TArray<FString> TestItems = {
		TEXT("Apple"),
		TEXT("HealthPotion"),
		TEXT("Wood"),
		TEXT("Stone"),
		TEXT("IronAxe")
	};

	for (int32 i = 0; i < TestItems.Num() && i < 10; i++)
	{
		FInventoryItem TestItem;

		if (TestItems[i] == TEXT("Apple"))
		{
			TestItem = UItemDatabase::CreateApple(1);
		}
		else if (TestItems[i] == TEXT("HealthPotion"))
		{
			TestItem = UItemDatabase::CreateHealthPotion(1);
		}
		else if (TestItems[i] == TEXT("Wood"))
		{
			TestItem = UItemDatabase::CreateWood(5);
		}
		else if (TestItems[i] == TEXT("Stone"))
		{
			TestItem = UItemDatabase::CreateStone(5);
		}
		else if (TestItems[i] == TEXT("IronAxe"))
		{
			TestItem = UItemDatabase::CreateIronAxe();
		}

		if (TestItem.IsValid())
		{
			int32 SlotIndex;
			bool bSuccess = InventoryComponent->AddItem(TestItem, SlotIndex);
			UE_LOG(LogTemp, Warning, TEXT("TestInventorySlots: Item %d (%s): %s - Slot %d"),
				i, *TestItems[i], bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), SlotIndex);
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("=== TEST COMPLETE ==="));
}

void ABlackTideCharacter::DiagnoseInventoryIssue()
{
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("DiagnoseInventoryIssue: No InventoryComponent!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("=== COMPREHENSIVE INVENTORY DIAGNOSIS ==="));

	// Step 1: Verify inventory initialization
	UE_LOG(LogTemp, Warning, TEXT("STEP 1: Verifying inventory initialization"));
	InventoryComponent->VerifySlotIntegrity();

	// Step 2: Clear inventory and show initial state
	UE_LOG(LogTemp, Warning, TEXT("STEP 2: Clearing inventory"));
	for (int32 i = 0; i < InventoryComponent->InventorySlots.Num(); i++)
	{
		if (!InventoryComponent->IsSlotEmpty(i))
		{
			InventoryComponent->RemoveItem(i, -1);
		}
	}
	InventoryComponent->DebugSlotStates();

	// Step 3: Test sequential item additions with detailed tracing
	UE_LOG(LogTemp, Warning, TEXT("STEP 3: Testing sequential item additions"));

	TArray<FString> TestSequence = {
		TEXT("Apple_1"),
		TEXT("Apple_2"),
		TEXT("HealthPotion_1"),
		TEXT("Apple_3"),
		TEXT("Wood_1"),
		TEXT("HealthPotion_2")
	};

	for (int32 TestIndex = 0; TestIndex < TestSequence.Num(); TestIndex++)
	{
		UE_LOG(LogTemp, Warning, TEXT("--- TEST %d: Adding %s ---"), TestIndex + 1, *TestSequence[TestIndex]);

		// Show state before addition
		UE_LOG(LogTemp, Warning, TEXT("Before addition - Empty slots: %d"), CountEmptySlots());

		FInventoryItem TestItem;
		if (TestSequence[TestIndex].Contains(TEXT("Apple")))
		{
			TestItem = UItemDatabase::CreateApple(1);
		}
		else if (TestSequence[TestIndex].Contains(TEXT("HealthPotion")))
		{
			TestItem = UItemDatabase::CreateHealthPotion(1);
		}
		else if (TestSequence[TestIndex].Contains(TEXT("Wood")))
		{
			TestItem = UItemDatabase::CreateWood(5);
		}

		if (TestItem.IsValid())
		{
			int32 SlotIndex;
			bool bSuccess = InventoryComponent->AddItem(TestItem, SlotIndex);

			UE_LOG(LogTemp, Warning, TEXT("Addition result: %s - Slot: %d"),
				bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"), SlotIndex);

			// Show state after addition
			UE_LOG(LogTemp, Warning, TEXT("After addition - Empty slots: %d"), CountEmptySlots());

			// Brief slot state check
			if (bSuccess && InventoryComponent->InventorySlots.IsValidIndex(SlotIndex))
			{
				const FInventorySlot& Slot = InventoryComponent->InventorySlots[SlotIndex];
				UE_LOG(LogTemp, Warning, TEXT("Slot %d state: Empty=%s, ItemValid=%s, ItemName='%s'"),
					SlotIndex, Slot.IsEmpty() ? TEXT("YES") : TEXT("NO"),
					Slot.Item.IsValid() ? TEXT("YES") : TEXT("NO"), *Slot.Item.Name);
			}
		}

		UE_LOG(LogTemp, Warning, TEXT("--- END TEST %d ---"), TestIndex + 1);
	}

	// Step 4: Final state analysis
	UE_LOG(LogTemp, Warning, TEXT("STEP 4: Final state analysis"));
	InventoryComponent->DebugSlotStates();

	UE_LOG(LogTemp, Warning, TEXT("=== DIAGNOSIS COMPLETE ==="));
}

int32 ABlackTideCharacter::CountEmptySlots() const
{
	if (!InventoryComponent) return 0;

	int32 EmptyCount = 0;
	for (int32 i = 0; i < InventoryComponent->InventorySlots.Num(); i++)
	{
		if (InventoryComponent->InventorySlots[i].IsEmpty())
		{
			EmptyCount++;
		}
	}
	return EmptyCount;
}

void ABlackTideCharacter::TraceUIUpdates()
{
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("TraceUIUpdates: No InventoryComponent!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("=== TRACING UI UPDATE CHAIN ==="));

	// Clear inventory first
	for (int32 i = 0; i < InventoryComponent->InventorySlots.Num(); i++)
	{
		if (!InventoryComponent->IsSlotEmpty(i))
		{
			InventoryComponent->RemoveItem(i, -1);
		}
	}

	// Test specific sequence that should trigger UI updates for slots 0, 1, 2, 3
	UE_LOG(LogTemp, Warning, TEXT("--- Testing UI updates for slots 0-3 ---"));

	// Add Apple to slot 0
	UE_LOG(LogTemp, Warning, TEXT("🧪 TEST 1: Adding Apple (should go to slot 0)"));
	FInventoryItem Apple1 = UItemDatabase::CreateApple(1);
	int32 SlotIndex1;
	InventoryComponent->AddItem(Apple1, SlotIndex1);

	// Add Health Potion to slot 1
	UE_LOG(LogTemp, Warning, TEXT("🧪 TEST 2: Adding Health Potion (should go to slot 1)"));
	FInventoryItem HealthPotion1 = UItemDatabase::CreateHealthPotion(1);
	int32 SlotIndex2;
	InventoryComponent->AddItem(HealthPotion1, SlotIndex2);

	// Add Wood to slot 2
	UE_LOG(LogTemp, Warning, TEXT("🧪 TEST 3: Adding Wood (should go to slot 2)"));
	FInventoryItem Wood1 = UItemDatabase::CreateWood(5);
	int32 SlotIndex3;
	InventoryComponent->AddItem(Wood1, SlotIndex3);

	// Add Stone to slot 3 - This is where UI updates often fail
	UE_LOG(LogTemp, Warning, TEXT("🧪 TEST 4: Adding Stone (should go to slot 3) - CRITICAL TEST"));
	FInventoryItem Stone1 = UItemDatabase::CreateStone(5);
	int32 SlotIndex4;
	InventoryComponent->AddItem(Stone1, SlotIndex4);

	UE_LOG(LogTemp, Warning, TEXT("=== UI UPDATE TRACE COMPLETE ==="));
	UE_LOG(LogTemp, Warning, TEXT("Results: Apple→%d, HealthPotion→%d, Wood→%d, Stone→%d"),
		SlotIndex1, SlotIndex2, SlotIndex3, SlotIndex4);
}

// Resource gathering test commands
void ABlackTideCharacter::AddYuccaRoot(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem YuccaRoot = UItemDatabase::CreateYuccaRoot(Amount);
		int32 SlotIndex;
		if (InventoryComponent->AddItem(YuccaRoot, SlotIndex))
		{
			UE_LOG(LogTemp, Log, TEXT("Added %d Yucca Root to slot %d"), Amount, SlotIndex);
		}
	}
}

void ABlackTideCharacter::AddWildBerries(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Berries = UItemDatabase::CreateWildBerries(Amount);
		int32 SlotIndex;
		if (InventoryComponent->AddItem(Berries, SlotIndex))
		{
			UE_LOG(LogTemp, Log, TEXT("Added %d Wild Berries to slot %d"), Amount, SlotIndex);
		}
	}
}

void ABlackTideCharacter::AddWoodSticks(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Sticks = UItemDatabase::CreateWoodSticks(Amount);
		int32 SlotIndex;
		if (InventoryComponent->AddItem(Sticks, SlotIndex))
		{
			UE_LOG(LogTemp, Log, TEXT("Added %d Wood Sticks to slot %d"), Amount, SlotIndex);
		}
	}
}

void ABlackTideCharacter::AddPlantFiber2(int32 Amount)
{
	if (InventoryComponent)
	{
		FInventoryItem Fiber = UItemDatabase::CreatePlantFiber2(Amount);
		int32 SlotIndex;
		if (InventoryComponent->AddItem(Fiber, SlotIndex))
		{
			UE_LOG(LogTemp, Log, TEXT("Added %d Plant Fiber to slot %d"), Amount, SlotIndex);
		}
	}
}

void ABlackTideCharacter::SpawnTestTree()
{
	if (!GetWorld())
	{
		return;
	}

	// Spawn tree in front of player
	FVector SpawnLocation = GetActorLocation() + (GetActorForwardVector() * 300.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideResourceNode* TreeNode = GetWorld()->SpawnActor<ABlackTideResourceNode>(SpawnLocation, SpawnRotation);
	if (TreeNode)
	{
		TreeNode->SetupAsTree(3, 120.0f); // 3 harvests, 2 minutes respawn
		UE_LOG(LogTemp, Log, TEXT("Spawned test tree at %s"), *SpawnLocation.ToString());
	}
}

void ABlackTideCharacter::SpawnTestRock()
{
	if (!GetWorld())
	{
		return;
	}

	// Spawn rock to the right of player
	FVector SpawnLocation = GetActorLocation() + (GetActorRightVector() * 300.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideResourceNode* RockNode = GetWorld()->SpawnActor<ABlackTideResourceNode>(SpawnLocation, SpawnRotation);
	if (RockNode)
	{
		RockNode->SetupAsRock(5, 180.0f); // 5 harvests, 3 minutes respawn
		UE_LOG(LogTemp, Log, TEXT("Spawned test rock at %s"), *SpawnLocation.ToString());
	}
}

void ABlackTideCharacter::SpawnTestPlant()
{
	if (!GetWorld())
	{
		return;
	}

	// Spawn plant to the left of player
	FVector SpawnLocation = GetActorLocation() + (GetActorRightVector() * -300.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideResourceNode* PlantNode = GetWorld()->SpawnActor<ABlackTideResourceNode>(SpawnLocation, SpawnRotation);
	if (PlantNode)
	{
		PlantNode->SetupAsPlant(2, 60.0f); // 2 harvests, 1 minute respawn
		UE_LOG(LogTemp, Log, TEXT("Spawned test plant at %s"), *SpawnLocation.ToString());
	}
}

void ABlackTideCharacter::SpawnTestBush()
{
	if (!GetWorld())
	{
		return;
	}

	// Spawn bush behind player
	FVector SpawnLocation = GetActorLocation() + (GetActorForwardVector() * -300.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideResourceNode* BushNode = GetWorld()->SpawnActor<ABlackTideResourceNode>(SpawnLocation, SpawnRotation);
	if (BushNode)
	{
		BushNode->SetupAsBush(3, 90.0f); // 3 harvests, 1.5 minutes respawn
		UE_LOG(LogTemp, Log, TEXT("Spawned test bush at %s"), *SpawnLocation.ToString());
	}
}

void ABlackTideCharacter::SpawnResourceNode()
{
	if (!GetWorld())
	{
		return;
	}

	// Spawn basic resource node in front of player
	FVector SpawnLocation = GetActorLocation() + (GetActorForwardVector() * 200.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideResourceNode* ResourceNode = GetWorld()->SpawnActor<ABlackTideResourceNode>(SpawnLocation, SpawnRotation);
	if (ResourceNode)
	{
		// Leave it unconfigured for Blueprint setup
		UE_LOG(LogTemp, Log, TEXT("Spawned basic resource node at %s - configure in Blueprint!"), *SpawnLocation.ToString());
	}
}

void ABlackTideCharacter::TestSpawn()
{
	UE_LOG(LogTemp, Warning, TEXT("=== TEST SPAWN CALLED ==="));

	if (!GetWorld())
	{
		UE_LOG(LogTemp, Error, TEXT("No world!"));
		return;
	}

	// Spawn basic resource node in front of player
	FVector SpawnLocation = GetActorLocation() + (GetActorForwardVector() * 200.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideResourceNode* ResourceNode = GetWorld()->SpawnActor<ABlackTideResourceNode>(SpawnLocation, SpawnRotation);
	if (ResourceNode)
	{
		ResourceNode->SetupAsPlant(2, 60.0f); // Easy to test - no tool required
		UE_LOG(LogTemp, Warning, TEXT("SUCCESS: Spawned test plant at %s"), *SpawnLocation.ToString());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("FAILED: Could not spawn resource node"));
	}
}

// Crafting test commands
void ABlackTideCharacter::ListRecipes()
{
	UE_LOG(LogTemp, Warning, TEXT("=== LISTING ALL CRAFTING RECIPES ==="));
	UCraftingRecipeDatabase::ListAllRecipes();
}

void ABlackTideCharacter::CraftItem(const FString& RecipeID)
{
	if (!CraftingComponent)
	{
		UE_LOG(LogTemp, Error, TEXT("No CraftingComponent found!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("Attempting to craft: %s"), *RecipeID);

	if (CraftingComponent->CanCraftRecipe(RecipeID))
	{
		bool bSuccess = CraftingComponent->StartCrafting(RecipeID);
		UE_LOG(LogTemp, Warning, TEXT("Crafting started: %s"), bSuccess ? TEXT("SUCCESS") : TEXT("FAILED"));
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot craft '%s' - requirements not met"), *RecipeID);

		// Debug: Check what's missing
		UE_LOG(LogTemp, Warning, TEXT("- Has ingredients: %s"),
			CraftingComponent->HasRequiredIngredients(RecipeID) ? TEXT("YES") : TEXT("NO"));
		UE_LOG(LogTemp, Warning, TEXT("- Has tools: %s"),
			CraftingComponent->HasRequiredTools(RecipeID) ? TEXT("YES") : TEXT("NO"));
		UE_LOG(LogTemp, Warning, TEXT("- Has station: %s"),
			CraftingComponent->HasRequiredStation(RecipeID) ? TEXT("YES") : TEXT("NO"));
	}
}

void ABlackTideCharacter::SpawnWorkbench()
{
	UE_LOG(LogTemp, Warning, TEXT("=== SPAWNING WORKBENCH ==="));

	if (!GetWorld())
	{
		UE_LOG(LogTemp, Error, TEXT("No world!"));
		return;
	}

	// Spawn workbench in front of player
	FVector SpawnLocation = GetActorLocation() + (GetActorForwardVector() * 300.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideCraftingStation* Workbench = GetWorld()->SpawnActor<ABlackTideCraftingStation>(SpawnLocation, SpawnRotation);
	if (Workbench)
	{
		Workbench->SetupAsWorkbench();
		UE_LOG(LogTemp, Warning, TEXT("SUCCESS: Spawned workbench at %s"), *SpawnLocation.ToString());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("FAILED: Could not spawn workbench"));
	}
}

void ABlackTideCharacter::SpawnForge()
{
	UE_LOG(LogTemp, Warning, TEXT("=== SPAWNING FORGE ==="));

	if (!GetWorld())
	{
		UE_LOG(LogTemp, Error, TEXT("No world!"));
		return;
	}

	// Spawn forge in front of player
	FVector SpawnLocation = GetActorLocation() + (GetActorForwardVector() * 300.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	ABlackTideCraftingStation* Forge = GetWorld()->SpawnActor<ABlackTideCraftingStation>(SpawnLocation, SpawnRotation);
	if (Forge)
	{
		Forge->SetupAsForge();
		UE_LOG(LogTemp, Warning, TEXT("SUCCESS: Spawned forge at %s"), *SpawnLocation.ToString());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("FAILED: Could not spawn forge"));
	}
}

void ABlackTideCharacter::TestGathering()
{
	UE_LOG(LogTemp, Warning, TEXT("=== TEST GATHERING CALLED ==="));

	// Manually trigger interact
	FInputActionValue DummyValue;
	Interact(DummyValue);
}

// Server RPC implementations for sprint
bool ABlackTideCharacter::ServerStartSprint_Validate()
{
	return true;
}

void ABlackTideCharacter::ServerStartSprint_Implementation()
{
	StartSprint();
}

bool ABlackTideCharacter::ServerStopSprint_Validate()
{
	return true;
}

void ABlackTideCharacter::ServerStopSprint_Implementation()
{
	StopSprint();
}

// Multicast animation implementations
void ABlackTideCharacter::MulticastPlayJumpAnimation_Implementation()
{
	UE_LOG(LogTemp, Warning, TEXT("🌐 Multicast jump animation received"));
	PlayJumpAnimationLocal();
}

void ABlackTideCharacter::MulticastPlayAttackAnimation_Implementation(UAnimMontage* AttackMontage, float PlayRate)
{
	if (!AttackMontage)
	{
		UE_LOG(LogTemp, Warning, TEXT("🌐 Multicast attack animation - no montage provided"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("🌐 Multicast attack animation received: %s"), *AttackMontage->GetName());

	// Play animation on all clients
	if (AnimationComponent)
	{
		bool bAnimationStarted = AnimationComponent->PlayMontage(AttackMontage, PlayRate);
		if (bAnimationStarted)
		{
			UE_LOG(LogTemp, Warning, TEXT("🎬 Multicast attack animation started: %s"), *AttackMontage->GetName());
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("❌ Failed to start multicast attack animation"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("❌ No animation component for multicast attack animation"));
	}
}

void ABlackTideCharacter::MulticastStopAnimation_Implementation(UAnimMontage* MontageToStop)
{
	if (!MontageToStop)
	{
		UE_LOG(LogTemp, Warning, TEXT("🌐 Multicast stop animation - no montage provided"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("🌐 Multicast stop animation received: %s"), *MontageToStop->GetName());

	// Stop animation on all clients
	if (AnimationComponent)
	{
		AnimationComponent->StopCurrentAnimation();
		UE_LOG(LogTemp, Warning, TEXT("🛑 Multicast animation stopped: %s"), *MontageToStop->GetName());
	}
}
