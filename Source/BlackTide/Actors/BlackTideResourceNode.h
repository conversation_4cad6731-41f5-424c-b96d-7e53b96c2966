// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "../InventoryItem.h"
#include "BlackTideResourceNode.generated.h"

class UStaticMeshComponent;
class UBlackTideInteractableComponent;
class ABlackT<PERSON><PERSON>haracter;

/**
 * Structure defining what resources this node provides when harvested
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FResourceDrop
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	FString ItemID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	int32 MinAmount = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	int32 MaxAmount = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float DropChance = 1.0f;

	FResourceDrop()
	{
		ItemID = TEXT("");
		MinAmount = 1;
		MaxAmount = 1;
		DropChance = 1.0f;
	}
};

/**
 * Enum for different resource node types
 */
UENUM(BlueprintType)
enum class EResourceNodeType : uint8
{
	Tree			UMETA(DisplayName = "Tree"),
	Rock			UMETA(DisplayName = "Rock"),
	Plant			UMETA(DisplayName = "Plant"),
	Bush			UMETA(DisplayName = "Bush"),
	Ore				UMETA(DisplayName = "Ore"),
	Other			UMETA(DisplayName = "Other")
};

/**
 * Actor representing a harvestable resource in the world
 * Can be trees, rocks, plants, etc. that players can gather from
 * Designed to be inherited by Blueprints for easy configuration
 */
UCLASS(BlueprintType, Blueprintable)
class BLACKTIDE_API ABlackTideResourceNode : public AActor
{
	GENERATED_BODY()

public:
	ABlackTideResourceNode();
	virtual ~ABlackTideResourceNode();

protected:
	virtual void BeginPlay() override;

public:
	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* MeshComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UBlackTideInteractableComponent* InteractableComponent;

	// Resource settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	EResourceNodeType ResourceType = EResourceNodeType::Tree;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	TArray<FResourceDrop> ResourceDrops;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	int32 MaxHarvestCount = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	float RespawnTime = 300.0f; // 5 minutes

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	bool bRespawns = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	bool bDestroyOnHarvest = false;

	// Visual settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	UStaticMesh* HarvestedMesh = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	FVector HarvestedScale = FVector(0.5f, 0.5f, 0.1f);

	// Tree falling physics settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tree Falling")
	bool bEnableTreeFalling = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tree Falling")
	float FallForceMultiplier = 500.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tree Falling")
	float FallDuration = 3.0f;

	// Log spawning settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Log Spawning")
	bool bSpawnLogsOnFall = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Log Spawning")
	TSubclassOf<AActor> LogActorClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Log Spawning")
	int32 MinLogsToSpawn = 2;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Log Spawning")
	int32 MaxLogsToSpawn = 4;

	// Current state
	UPROPERTY(BlueprintReadOnly, Category = "State", Replicated)
	int32 CurrentHarvestCount = 0;

	UPROPERTY(BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_IsHarvested)
	bool bIsHarvested = false;

	UPROPERTY(BlueprintReadOnly, Category = "State", Replicated)
	bool bIsBeingHarvested = false;

	// Tree falling state (replicated for visual sync)
	UPROPERTY(BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_TreeFallingState)
	bool bIsFalling = false;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	float LastHarvestTime = 0.0f;

	// Functions
	UFUNCTION(BlueprintCallable, Category = "Resource")
	void HarvestResource(ABlackTideCharacter* HarvesterCharacter);

	UFUNCTION(BlueprintCallable, Category = "Resource")
	void ResetResource();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Resource")
	bool CanBeHarvested() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Resource")
	float GetRespawnProgress() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Resource")
	TArray<FInventoryItem> GenerateResourceDrops() const;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Resource")
	void OnResourceHarvested(ABlackTideCharacter* HarvesterCharacter);

	UFUNCTION(BlueprintImplementableEvent, Category = "Resource")
	void OnResourceRespawned();

	// Blueprint configuration helpers
	UFUNCTION(BlueprintCallable, Category = "Resource Setup")
	void SetupAsTree(int32 HarvestCount = 3, float RespawnTimeSeconds = 300.0f);

	UFUNCTION(BlueprintCallable, Category = "Resource Setup")
	void SetupAsRock(int32 HarvestCount = 5, float RespawnTimeSeconds = 600.0f);

	UFUNCTION(BlueprintCallable, Category = "Resource Setup")
	void SetupAsPlant(int32 HarvestCount = 2, float RespawnTimeSeconds = 120.0f);

	UFUNCTION(BlueprintCallable, Category = "Resource Setup")
	void SetupAsBush(int32 HarvestCount = 3, float RespawnTimeSeconds = 180.0f);

	UFUNCTION(BlueprintCallable, Category = "Resource Setup")
	void AddResourceDrop(const FString& ItemID, int32 MinAmount, int32 MaxAmount, float DropChance = 1.0f);

	// Tree falling functions
	UFUNCTION(BlueprintCallable, Category = "Tree Falling")
	void StartTreeFalling(ABlackTideCharacter* FellerCharacter);

	UFUNCTION(BlueprintCallable, Category = "Tree Falling")
	void SpawnLogsAtLocation(const FVector& Location);

	// Multicast functions for multiplayer
	UFUNCTION(NetMulticast, Reliable, Category = "Tree Falling")
	void MulticastStartTreeFalling(const FVector& FallDirection, ABlackTideCharacter* FellerCharacter);

	UFUNCTION(NetMulticast, Reliable, Category = "Tree Falling")
	void MulticastSpawnLogEffects(const FVector& Location, int32 NumLogs);

	UFUNCTION(NetMulticast, Reliable, Category = "Visual")
	void MulticastUpdateVisualState();

	// Replication functions
	UFUNCTION()
	void OnRep_IsHarvested();

	UFUNCTION()
	void OnRep_TreeFallingState();

protected:
	// Replication
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	// Internal functions
	void UpdateVisualState();
	void StartRespawnTimer();

	UFUNCTION()
	void OnInteractionCompleted(ABlackTideCharacter* Character);

	// Timer handle for respawn
	FTimerHandle RespawnTimerHandle;

	// Timer handle for tree falling animation
	FTimerHandle TreeFallTimerHandle;

	// Original mesh and scale for restoration
	UPROPERTY()
	UStaticMesh* OriginalMesh;

	FVector OriginalScale;
};
