// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "../InventoryItem.h"
#include "BlackTideWorldItem.generated.h"

/**
 * World item that can be picked up and added to inventory
 * Represents any item from ItemDataTable as a 3D object in the world
 */
UCLASS(BlueprintType, Blueprintable)
class BLACKTIDE_API ABlackTideWorldItem : public AActor
{
	GENERATED_BODY()

public:
	ABlackTideWorldItem();

protected:
	virtual void BeginPlay() override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* ItemMesh;

	// UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	// UBlackTideInteractableComponent* InteractableComponent;

	// Item data (replicated so all clients can see the item)
	UPROPERTY(ReplicatedUsing = OnRep_ItemData, EditAnywhere, BlueprintReadWrite, Category = "Item")
	FInventoryItem ItemData;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float LifeTime = 300.0f; // 5 minutes before despawn

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	bool bCanBePickedUp = true;

public:
	// Functions
	UFUNCTION(BlueprintCallable, Category = "World Item")
	void SetupItem(const FInventoryItem& Item, UStaticMesh* Mesh = nullptr);

	UFUNCTION(BlueprintCallable, Category = "World Item")
	void PickupItem(class ABlackTideCharacter* Character);

	UFUNCTION(BlueprintCallable, Category = "World Item")
	void DespawnItem();

	// Carry system functions
	UFUNCTION(BlueprintCallable, Category = "World Item")
	void PickupToInventory(class ABlackTideCharacter* Character);

	UFUNCTION(BlueprintCallable, Category = "World Item")
	void PickupToCarry(class ABlackTideCharacter* Character);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "World Item")
	FInventoryItem GetItemData() const { return ItemData; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "World Item")
	bool CanBePickedUp() const { return bCanBePickedUp; }

	// Server functions for multiplayer
	UFUNCTION(Server, Reliable, WithValidation, Category = "World Item")
	void ServerPickupItem(class ABlackTideCharacter* Character);

	// Replication functions
	UFUNCTION()
	void OnRep_ItemData();

protected:
	// Timer for despawn
	FTimerHandle DespawnTimer;

	// Overlap event
	UFUNCTION()
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
};
