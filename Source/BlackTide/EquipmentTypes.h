// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "EquipmentTypes.generated.h"

/**
 * Enum for equipment slot types
 */
UENUM(BlueprintType)
enum class EEquipmentSlot : uint8
{
	None			UMETA(DisplayName = "None"),
	MainHand		UMETA(DisplayName = "Main Hand"),		// Primary weapon/tool
	OffHand			UMETA(DisplayName = "Off Hand"),		// Secondary weapon/shield
	Head			UMETA(DisplayName = "Head"),			// Helmet/hat
	Chest			UMETA(DisplayName = "Chest"),			// Chest armor
	Legs			UMETA(DisplayName = "Legs"),			// Leg armor
	Feet			UMETA(DisplayName = "Feet"),			// Boots
	Back			UMETA(DisplayName = "Back"),			// Backpack/cloak
	Utility			UMETA(DisplayName = "Utility"),			// Tools/utility items

	// Carry system slots
	Shoulder		UMETA(DisplayName = "Shoulder"),		// For carrying logs, planks
	CarryHands		UMETA(DisplayName = "Carry Hands")		// For two-handed carrying
};
