// Copyright Epic Games, Inc. All Rights Reserved.

#include "CraftingRecipeWidget.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "Components/Image.h"
#include "Components/Border.h"
#include "Engine/Engine.h"

UCraftingRecipeWidget::UCraftingRecipeWidget(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	RecipeID = TEXT("");
	bCanCraft = false;
	bIsSelected = false;
}

void UCraftingRecipeWidget::NativeConstruct()
{
	Super::NativeConstruct();

	// Bind button event
	if (RecipeButton)
	{
		RecipeButton->OnClicked.AddDynamic(this, &UCraftingRecipeWidget::OnRecipeButtonClicked);
	}

	// Set initial appearance
	UpdateCraftableStatus(bCanCraft);
	SetSelected(bIsSelected);
}

void UCraftingRecipeWidget::SetupRecipe(const FString& InRecipeID, const FCraftingRecipeTableRow& InRecipeData)
{
	RecipeID = InRecipeID;
	RecipeData = InRecipeData;

	// Update recipe name
	if (RecipeNameText)
	{
		RecipeNameText->SetText(FText::FromString(RecipeData.RecipeName));
	}

	// Update recipe icon
	if (RecipeIcon && RecipeData.RecipeIcon.IsValid())
	{
		// TODO: Load and set recipe icon
		// RecipeIcon->SetBrushFromTexture(RecipeData.RecipeIcon.LoadSynchronous());
	}

	UE_LOG(LogTemp, Log, TEXT("CraftingRecipeWidget: Setup recipe '%s' - %s"), *RecipeID, *RecipeData.RecipeName);
}

void UCraftingRecipeWidget::UpdateCraftableStatus(bool bInCanCraft)
{
	bCanCraft = bInCanCraft;

	// Update craftable text
	if (CraftableText)
	{
		if (bCanCraft)
		{
			CraftableText->SetText(FText::FromString(TEXT("✓ Can Craft")));
			CraftableText->SetColorAndOpacity(FLinearColor::Green);
		}
		else
		{
			CraftableText->SetText(FText::FromString(TEXT("✗ Cannot Craft")));
			CraftableText->SetColorAndOpacity(FLinearColor::Red);
		}
	}

	// Update border color
	UpdateBorderColor();
}

void UCraftingRecipeWidget::SetSelected(bool bSelected)
{
	bIsSelected = bSelected;
	UpdateBorderColor();

	UE_LOG(LogTemp, VeryVerbose, TEXT("CraftingRecipeWidget: Recipe '%s' selected: %s"), *RecipeID, bSelected ? TEXT("true") : TEXT("false"));
}

void UCraftingRecipeWidget::UpdateBorderColor()
{
	if (!RecipeBorder)
	{
		return;
	}

	FLinearColor BorderColor;

	if (bIsSelected)
	{
		BorderColor = SelectedColor;
	}
	else if (!bCanCraft)
	{
		BorderColor = CannotCraftColor;
	}
	else
	{
		BorderColor = NormalColor;
	}

	RecipeBorder->SetBrushColor(BorderColor);
}

void UCraftingRecipeWidget::OnRecipeButtonClicked()
{
	UE_LOG(LogTemp, Log, TEXT("CraftingRecipeWidget: Recipe '%s' clicked"), *RecipeID);
	
	// Broadcast click event
	OnRecipeWidgetClicked.Broadcast(RecipeID);
}
