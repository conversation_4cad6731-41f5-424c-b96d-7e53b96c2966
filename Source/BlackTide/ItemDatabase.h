// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "InventoryItem.h"
#include "Engine/DataTable.h"
#include "Actors/BlackTideBuildingPiece.h"
#include "EquipmentTypes.h"
#include "ItemDatabase.generated.h"

// NOTE: FItemDefinition has been removed - use FItemTableRow instead
// This eliminates duplicate struct definitions and ensures single source of truth

/**
 * Item rarity/quality system
 */
UENUM(BlueprintType)
enum class EItemRarity : uint8
{
	Common			UMETA(DisplayName = "Common"),			// White
	Uncommon		UMETA(DisplayName = "Uncommon"),		// Green
	Rare			UMETA(DisplayName = "Rare"),			// Blue
	Epic			UMETA(DisplayName = "Epic"),			// Purple
	Legendary		UMETA(DisplayName = "Legendary"),		// Orange
	Artifact		UMETA(DisplayName = "Artifact")			// Red
};

/**
 * Defines how an item can be carried/stored
 */
UENUM(BlueprintType)
enum class EItemCarryType : uint8
{
	InventoryOnly	UMETA(DisplayName = "Inventory Only"),		// Small items (berries, tools, coins)
	CarryOnly		UMETA(DisplayName = "Carry Only"),			// Large items (logs, planks, stones)
	Both			UMETA(DisplayName = "Both")					// Medium items (weapons, some materials)
};

/**
 * Item requirements structure
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FItemRequirements
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	int32 MinLevel = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	TArray<FString> RequiredSkills;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	TArray<FString> RequiredItems;

	FItemRequirements()
	{
		MinLevel = 1;
		RequiredSkills.Empty();
		RequiredItems.Empty();
	}
};

/**
 * Armor protection values
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FArmorStats
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Armor")
	float PhysicalProtection = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Armor")
	float ElementalProtection = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Armor")
	float ColdResistance = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Armor")
	float HeatResistance = 0.0f;

	FArmorStats()
	{
		PhysicalProtection = 0.0f;
		ElementalProtection = 0.0f;
		ColdResistance = 0.0f;
		HeatResistance = 0.0f;
	}
};

/**
 * Tool efficiency for resource gathering
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FToolEfficiency
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tool")
	TMap<FString, float> ResourceEfficiency; // ResourceType -> Efficiency multiplier

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tool")
	float BaseGatherSpeed = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tool")
	float DurabilityLossPerUse = 1.0f;

	FToolEfficiency()
	{
		ResourceEfficiency.Empty();
		BaseGatherSpeed = 1.0f;
		DurabilityLossPerUse = 1.0f;
	}
};

/**
 * Data table row structure for item definitions
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FItemTableRow : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FString Name;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FString Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	EItemType ItemType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	TSoftObjectPtr<UTexture2D> Icon;

	// Note: All items are single-slot (1x1) in BlackTide

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	int32 MaxStackSize = 1;

	// 3D World Representation
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "3D Representation")
	TSoftObjectPtr<UStaticMesh> WorldMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "3D Representation")
	FTransform WorldMeshTransform = FTransform::Identity;

	// Held Item Animation
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> HoldingAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TSoftObjectPtr<UAnimMontage> UseAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	int32 Value = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float Weight = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float MaxDurability = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FConsumableEffect ConsumableEffect;

	// NEW: Item quality and requirements
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Quality")
	EItemRarity Rarity = EItemRarity::Common;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Requirements")
	FItemRequirements Requirements;

	// NEW: Armor properties (for Armor type items)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Armor",
		meta = (EditCondition = "ItemType == EItemType::Armor", EditConditionHides))
	FArmorStats ArmorStats;

	// NEW: Tool efficiency (for Tool type items)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tool Efficiency",
		meta = (EditCondition = "ItemType == EItemType::Tool", EditConditionHides))
	FToolEfficiency ToolEfficiency;

	// NEW: Crafting recipe information
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crafting")
	TArray<FString> CraftingStations; // Which crafting stations can make this item

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crafting")
	TMap<FString, int32> CraftingMaterials; // ItemID -> Quantity required

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crafting")
	float CraftingTime = 5.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crafting")
	int32 CraftingYield = 1; // How many items are produced per craft

	// Building properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building")
	EBuildingPieceType BuildingType = EBuildingPieceType::Foundation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building")
	FVector BuildingSize = FVector(100.0f, 100.0f, 100.0f);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building")
	bool bCanSnapToOthers = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building")
	TSoftClassPtr<class ABlackTideBuildingPiece> BuildingPieceClass;

	// 3D model for building preview/inventory display
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building")
	TSoftObjectPtr<UStaticMesh> BuildingPreviewMesh;

	// Material override for building preview
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building")
	TSoftObjectPtr<UMaterialInterface> BuildingPreviewMaterial;

	// Equipment properties - Choose ONE system per item:

	// OPTION A: Simple equipment (armor, accessories) - uses static mesh
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simple Equipment")
	TSoftObjectPtr<UStaticMesh> EquippedMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simple Equipment")
	FName PreferredSocket = NAME_None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simple Equipment")
	FTransform EquipmentTransform = FTransform::Identity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simple Equipment")
	EEquipmentSlot PreferredSlot = EEquipmentSlot::None;

	// OPTION B: Advanced weapons/tools - uses Blueprint with all data inside
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Weapon")
	TSoftClassPtr<class ABlackTideWeaponBase> WeaponBlueprint;

	// NOTE: Animation, damage, collision etc. are ALL handled inside the WeaponBlueprint
	// No duplication - weapon BP is the single source of truth!

	// Carry System Properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry System")
	EItemCarryType CarryType = EItemCarryType::InventoryOnly;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry System")
	EEquipmentSlot PreferredCarrySlot = EEquipmentSlot::Shoulder;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry System")
	TSoftObjectPtr<UStaticMesh> CarryMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry System")
	FTransform CarryTransform = FTransform::Identity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry System", meta = (ClampMin = "0.1", ClampMax = "1.0"))
	float CarryMovementSpeedMultiplier = 0.7f; // 30% slower when carrying

	// Building Placement Properties (for carry-only items that can be placed)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry Building")
	bool bCanBePlacedAsBuilding = false;

	// Note: Building class reference temporarily disabled to avoid potential issues
	// UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Carry Building")
	// TSoftClassPtr<class ABlackTideBuildingPiece> PlaceableBuildingClass;

	FItemTableRow()
	{
		Name = TEXT("Unknown Item");
		Description = TEXT("No description available");
		ItemType = EItemType::None;
		Icon = nullptr;
		MaxStackSize = 1;
		Value = 0;
		Weight = 0.0f;
		MaxDurability = 100.0f;

		// Initialize new fields
		Rarity = EItemRarity::Common;
		Requirements = FItemRequirements();
		ArmorStats = FArmorStats();
		ToolEfficiency = FToolEfficiency();
		CraftingStations.Empty();
		CraftingMaterials.Empty();
		CraftingTime = 5.0f;
		CraftingYield = 1;

		// Building properties
		BuildingType = EBuildingPieceType::Foundation;
		BuildingSize = FVector(100.0f, 100.0f, 100.0f);
		bCanSnapToOthers = true;
		BuildingPieceClass = nullptr;
		BuildingPreviewMesh = nullptr;
		BuildingPreviewMaterial = nullptr;

		// Equipment properties
		EquippedMesh = nullptr;
		PreferredSocket = NAME_None;
		EquipmentTransform = FTransform::Identity;
		PreferredSlot = EEquipmentSlot::None;
		WeaponBlueprint = nullptr;

		// Carry system properties
		CarryType = EItemCarryType::InventoryOnly;
		PreferredCarrySlot = EEquipmentSlot::Shoulder;
		CarryMesh = nullptr;
		CarryTransform = FTransform::Identity;
		CarryMovementSpeedMultiplier = 0.7f;
		bCanBePlacedAsBuilding = false;
		// PlaceableBuildingClass = nullptr; // Temporarily disabled
	}
};

/**
 * Static item database for creating and managing items
 */
UCLASS(BlueprintType, Blueprintable)
class BLACKTIDE_API UItemDatabase : public UObject
{
	GENERATED_BODY()

public:
	UItemDatabase();

	// Create item from ID
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateItem(const FString& ItemID, int32 StackSize = 1);

	// Get item definition from data table (C++ only - not exposed to Blueprint)
	static FItemTableRow* GetItemDefinition(const FString& ItemID);

	// 3D World Item Functions
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static class ABlackTideWorldItem* SpawnWorldItem(UWorld* World, const FString& ItemID, const FVector& Location, int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static UStaticMesh* GetItemWorldMesh(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FVector GetItemWorldScale(const FString& ItemID);

	// Carry System Functions
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static bool CanItemBeCarried(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static bool CanItemGoInInventory(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static EItemCarryType GetItemCarryType(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static EEquipmentSlot GetPreferredCarrySlot(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static UStaticMesh* GetItemCarryMesh(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static float GetCarryMovementSpeedMultiplier(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static bool CanItemBePlacedAsBuilding(const FString& ItemID);

	// Development/Setup Functions
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static void ClassifyExistingItems();

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static UAnimMontage* GetItemHoldingAnimation(const FString& ItemID);

	// NEW: Utility functions for enhanced item system
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static bool CanPlayerUseItem(const FString& ItemID, int32 PlayerLevel, const TArray<FString>& PlayerSkills);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static float GetToolEfficiencyForResource(const FString& ToolItemID, const FString& ResourceType);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FLinearColor GetRarityColor(EItemRarity Rarity);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static TArray<FString> GetCraftingMaterialsForItem(const FString& ItemID);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static bool CanCraftItem(const FString& ItemID, const TMap<FString, int32>& AvailableMaterials);

	// Helper function to load icon by name
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static TSoftObjectPtr<UTexture2D> LoadItemIcon(const FString& IconName);

	// Debug function to test icon loading
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static void DebugTestIconLoading(const FString& IconName);

	// Helper function to create DataTable asset (Editor only)
	UFUNCTION(BlueprintCallable, Category = "Item Database", CallInEditor)
	static void CreateItemDataTableAsset();

	// List all available items in DataTable
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static void ListAllItems();

	// ===== DEPRECATED FUNCTIONS =====
	// These functions are deprecated - use CreateItem() with DataTable instead
	// TODO: Remove these functions once all items are in DataTable
	//
	// ⚠️  IMPORTANT: Only use CreateItem(ItemID, StackSize) for new code!
	//     All items must be defined in DataTable for proper multiplayer support

	UFUNCTION(BlueprintCallable, Category = "Item Database", meta = (DeprecatedFunction, DeprecationMessage = "Use CreateItem() with DataTable instead"))
	static FInventoryItem CreateApple(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateWaterBottle(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateWood(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateStone(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateIronAxe();

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateIronPickaxe();

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateHealthPotion(int32 StackSize = 1);

	// === FOOD & CONSUMABLES ===
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateCoconut(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateJuccaRoot(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateCookedJucca(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateBerries(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateRawCrab(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateCookedCrab(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateDirtyWater(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateBoiledWater(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateHerbs(int32 StackSize = 1);

	// === RESOURCES ===
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateWoodLog(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateWoodPlank(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateStoneChunk(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateRope(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateCoconutHusk(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreatePlantFiber(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateClay(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateFlint(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreatePalmLeaf(int32 StackSize = 1);

	// Resource gathering items
	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateYuccaRoot(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateWildBerries(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreateWoodSticks(int32 StackSize = 1);

	UFUNCTION(BlueprintCallable, Category = "Item Database")
	static FInventoryItem CreatePlantFiber2(int32 StackSize = 1);

protected:
	// Reference to item data table (can be set in Blueprint)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Database")
	UDataTable* ItemDataTable;

private:
	// Helper function to create item from definition
	static FInventoryItem CreateItemFromDefinition(const FString& ItemID, const FItemTableRow& Definition, int32 StackSize);
};
