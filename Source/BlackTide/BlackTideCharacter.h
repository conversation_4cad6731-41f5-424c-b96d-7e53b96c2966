// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "Logging/LogMacros.h"
#include "BlackTideCharacter.generated.h"

class USpringArmComponent;
class UCameraComponent;
class UInputMappingContext;
class UInputAction;
class USurvivalComponent;
class UInventoryComponent;
struct FInputActionValue;

// Forward declarations for BlackTide components
class UBlackTideGatheringComponent;
class UBlackTideCraftingComponent;
class UBlackTideEquipmentComponent;
class UBlackTideAnimationComponent;
class UBlackTideWeaponHitComponent;

DECLARE_LOG_CATEGORY_EXTERN(LogTemplateCharacter, Log, All);

UCLASS(config=Game)
class ABlackTideCharacter : public ACharacter
{
	GENERATED_BODY()

	/** Camera boom positioning the camera behind the character */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera, meta = (AllowPrivateAccess = "true"))
	USpringArmComponent* CameraBoom;

	/** Follow camera */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera, meta = (AllowPrivateAccess = "true"))
	UCameraComponent* FollowCamera;

	/** Survival Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Survival", meta = (AllowPrivateAccess = "true"))
	USurvivalComponent* SurvivalComponent;

	/** Inventory Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Inventory", meta = (AllowPrivateAccess = "true"))
	UInventoryComponent* InventoryComponent;

	/** Gathering Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Gathering", meta = (AllowPrivateAccess = "true"))
	UBlackTideGatheringComponent* GatheringComponent;

	/** Crafting Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Crafting", meta = (AllowPrivateAccess = "true"))
	UBlackTideCraftingComponent* CraftingComponent;

	/** Equipment Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Equipment", meta = (AllowPrivateAccess = "true"))
	UBlackTideEquipmentComponent* EquipmentComponent;

	/** Animation Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Animation", meta = (AllowPrivateAccess = "true"))
	UBlackTideAnimationComponent* AnimationComponent;

	/** Weapon Hit Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat", meta = (AllowPrivateAccess = "true"))
	UBlackTideWeaponHitComponent* WeaponHitComponent;

	/** MappingContext */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputMappingContext* DefaultMappingContext;

	/** Jump Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* JumpAction;

	/** Move Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* MoveAction;

	/** Look Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* LookAction;

	/** Inventory Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* InventoryAction;

	/** Sprint Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* SprintAction;

	/** Interact Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* InteractAction;

	/** Toggle Weapon Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* ToggleWeaponAction;

	/** Melee Attack Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* MeleeAttackAction;

	/** Crafting Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* CraftingAction;

public:
	ABlackTideCharacter();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	virtual void Landed(const FHitResult& Hit) override;

public:
	// Survival functions
	UFUNCTION(BlueprintCallable, Category = "Survival")
	USurvivalComponent* GetSurvivalComponent() const { return SurvivalComponent; }

	// Inventory functions
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	UInventoryComponent* GetInventoryComponent() const { return InventoryComponent; }

	// Gathering functions
	UFUNCTION(BlueprintCallable, Category = "Gathering")
	UBlackTideGatheringComponent* GetGatheringComponent() const { return GatheringComponent; }

	// Crafting functions
	UFUNCTION(BlueprintCallable, Category = "Crafting")
	UBlackTideCraftingComponent* GetCraftingComponent() const { return CraftingComponent; }

	// Equipment functions
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	UBlackTideEquipmentComponent* GetEquipmentComponent() const { return EquipmentComponent; }

	// Animation functions
	UFUNCTION(BlueprintCallable, Category = "Animation")
	UBlackTideAnimationComponent* GetAnimationComponent() const { return AnimationComponent; }

	// Weapon hit functions
	UFUNCTION(BlueprintCallable, Category = "Combat")
	UBlackTideWeaponHitComponent* GetWeaponHitComponent() const { return WeaponHitComponent; }

	// Test commands for inventory
	UFUNCTION(Exec, Category = "Inventory")
	void AddApple(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddWood(int32 Amount = 10);

	UFUNCTION(Exec, Category = "Inventory")
	void AddStone(int32 Amount = 10);

	UFUNCTION(Exec, Category = "Inventory")
	void AddIronAxe();

	UFUNCTION(Exec, Category = "Equipment")
	void EquipAxe();

	UFUNCTION(Exec, Category = "Inventory")
	void AddIronPickaxe();

	UFUNCTION(Exec, Category = "Equipment")
	void EquipPickaxe();

	// Universal item adding command - works with any item in DataTable
	UFUNCTION(Exec, Category = "Inventory")
	void AddItem(const FString& ItemID, int32 Amount = 1);

	// List all available items in DataTable
	UFUNCTION(Exec, Category = "Inventory")
	void ListItems();

	UFUNCTION(Exec, Category = "Inventory")
	void AddHealthPotion(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddWaterBottle(int32 Amount = 1);

	// === FOOD & CONSUMABLES ===
	UFUNCTION(Exec, Category = "Inventory")
	void AddCoconut(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddJuccaRoot(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddCookedJucca(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddBerries(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddRawCrab(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddCookedCrab(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddDirtyWater(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddBoiledWater(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddHerbs(int32 Amount = 1);

	// === RESOURCES ===
	UFUNCTION(Exec, Category = "Inventory")
	void AddWoodLog(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddWoodPlank(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddStoneChunk(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddRope(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddCoconutHusk(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddPlantFiber(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddClay(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddFlint(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void AddPalmLeaf(int32 Amount = 1);

	UFUNCTION(Exec, Category = "Inventory")
	void ClearInventory();

	UFUNCTION(Exec, Category = "Inventory")
	void RefreshInventoryUI();

	UFUNCTION(Exec, Category = "Inventory")
	void ToggleInventory();

	UFUNCTION(Exec, Category = "Crafting")
	void ToggleCrafting();

	// Crafting UI functions
	UFUNCTION(BlueprintCallable, Category = "Crafting UI")
	void OpenCraftingPanel();

	UFUNCTION(BlueprintCallable, Category = "Crafting UI")
	void CloseCraftingPanel();

	UFUNCTION(BlueprintCallable, Category = "Crafting UI")
	bool IsCraftingPanelOpen() const;

	UFUNCTION(Exec, Category = "Inventory")
	void DebugInventory();

	UFUNCTION(Exec, Category = "Inventory")
	void InitInventory();

	UFUNCTION(Exec, Category = "Inventory")
	void TestInventorySlots();

	UFUNCTION(Exec, Category = "Inventory")
	void DiagnoseInventoryIssue();

	UFUNCTION(Exec, Category = "Inventory")
	void TraceUIUpdates();

	UFUNCTION(Exec, Category = "Inventory")
	void ValidateInventoryState();

	UFUNCTION(Exec, Category = "Inventory")
	void DebugEventBindings();

	// Resource gathering test commands
	UFUNCTION(Exec, Category = "Gathering")
	void AddYuccaRoot(int32 Amount = 5);

	UFUNCTION(Exec, Category = "Gathering")
	void AddWildBerries(int32 Amount = 10);

	UFUNCTION(Exec, Category = "Gathering")
	void AddWoodSticks(int32 Amount = 15);

	UFUNCTION(Exec, Category = "Gathering")
	void AddPlantFiber2(int32 Amount = 20);

	UFUNCTION(Exec, Category = "Gathering")
	void SpawnTestTree();

	UFUNCTION(Exec, Category = "Gathering")
	void SpawnTestRock();

	UFUNCTION(Exec, Category = "Gathering")
	void SpawnTestPlant();

	UFUNCTION(Exec, Category = "Gathering")
	void SpawnTestBush();

	UFUNCTION(Exec, Category = "Gathering")
	void SpawnResourceNode();

	// Simple test function
	UFUNCTION(Exec)
	void TestSpawn();

	// Crafting test commands
	UFUNCTION(Exec, Category = "Crafting")
	void ListRecipes();

	UFUNCTION(Exec, Category = "Crafting")
	void CraftItem(const FString& RecipeID);

	UFUNCTION(Exec, Category = "Crafting")
	void SpawnWorkbench();

	UFUNCTION(Exec, Category = "Crafting")
	void SpawnForge();

	UFUNCTION(Exec)
	void TestGathering();

	// Helper function for diagnostics
	int32 CountEmptySlots() const;

	UFUNCTION(BlueprintCallable, Category = "Survival")
	float GetSurvivalMovementMultiplier() const;

	UFUNCTION(BlueprintCallable, Category = "Survival")
	void ApplySurvivalEffectsToMovement();

	UFUNCTION()
	void OnSurvivalEffectChanged(bool bHasNegativeEffects);

	// Jump with animation sync
	virtual void Jump() override;

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void ExecuteDelayedJump();

	// Local animation playback
	void PlayJumpAnimationLocal();

	// World item interaction
	UFUNCTION(BlueprintCallable, Category = "Interaction")
	class ABlackTideWorldItem* FindNearestWorldItem() const;

	// Carry system functions
	UFUNCTION(BlueprintCallable, Category = "Carry System")
	void DropCarriedItem();

	// Sprint functions
	UFUNCTION(BlueprintCallable, Category = "Movement")
	void StartSprint();

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void StopSprint();

	// Server sprint functions
	UFUNCTION(Server, Reliable, WithValidation, Category = "Movement")
	void ServerStartSprint();

	UFUNCTION(Server, Reliable, WithValidation, Category = "Movement")
	void ServerStopSprint();

	// Multicast animation functions
	UFUNCTION(NetMulticast, Reliable, Category = "Animation")
	void MulticastPlayJumpAnimation();

	UFUNCTION(NetMulticast, Reliable, Category = "Animation")
	void MulticastPlayAttackAnimation(UAnimMontage* AttackMontage, float PlayRate);

	UFUNCTION(NetMulticast, Reliable, Category = "Animation")
	void MulticastStopAnimation(UAnimMontage* MontageToStop);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Movement")
	bool IsSprinting() const { return bIsSprinting; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Movement")
	bool CanSprint() const;

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void UpdateMovementSpeed();

	// Jump/Landing functions
	UFUNCTION(BlueprintCallable, Category = "Animation")
	void HandleJumpLanding();

	UFUNCTION(BlueprintCallable, Category = "Animation")
	void StopJumpMontage();

	// Animation state for jump (without physics)
	UPROPERTY(BlueprintReadOnly, Category = "Animation")
	bool bWantsToJump;

	// Pre-jump animation state (for anticipation)
	UPROPERTY(BlueprintReadOnly, Category = "Animation")
	bool bIsPreJumping;

	// Jump montage state tracking
	UPROPERTY(BlueprintReadOnly, Category = "Animation")
	bool bIsPlayingJumpMontage;

	// Sprint state
	UPROPERTY(BlueprintReadOnly, Category = "Movement", Replicated)
	bool bIsSprinting;



protected:

	/** Called for movement input */
	void Move(const FInputActionValue& Value);

	/** Called for looking input */
	void Look(const FInputActionValue& Value);

	/** Called for inventory input */
	void OpenInventory(const FInputActionValue& Value);

	/** Called for interact input */
	void Interact(const FInputActionValue& Value);

	/** Called for toggle weapon input */
	void ToggleWeapon(const FInputActionValue& Value);

	/** Called for melee attack input */
	void PerformMeleeAttack(const FInputActionValue& Value);

	/** Called for crafting input */
	void OpenCraftingUI(const FInputActionValue& Value);

private:
	// Input debouncing
	float LastInventoryToggleTime = 0.0f;
	float InventoryToggleCooldown = 0.2f; // 200ms cooldown

	// UI References
	UPROPERTY()
	class UCraftingPanelWidget* CraftingPanelWidget;

	// Widget Classes (set in Blueprint)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI", meta = (AllowPrivateAccess = "true"))
	TSubclassOf<class UCraftingPanelWidget> CraftingPanelWidgetClass;
			

protected:

	virtual void NotifyControllerChanged() override;

	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

public:
	/** Returns CameraBoom subobject **/
	FORCEINLINE class USpringArmComponent* GetCameraBoom() const { return CameraBoom; }
	/** Returns FollowCamera subobject **/
	FORCEINLINE class UCameraComponent* GetFollowCamera() const { return FollowCamera; }
	/** Returns SurvivalComponent subobject **/
	FORCEINLINE class USurvivalComponent* GetSurvivalComponentDirect() const { return SurvivalComponent; }

private:
	// Survival state tracking
	float BaseWalkSpeed;
	float BaseRunSpeed;
	bool bSurvivalEffectsApplied;

	// Sprint settings
	UPROPERTY(EditDefaultsOnly, Category = "Sprint Settings")
	float SprintSpeed;

	UPROPERTY(EditDefaultsOnly, Category = "Sprint Settings")
	float SprintStaminaDrainRate;

	UPROPERTY(EditDefaultsOnly, Category = "Sprint Settings")
	float MinStaminaToSprint;

	UPROPERTY(EditDefaultsOnly, Category = "Sprint Settings")
	bool bRequireMovementToSprint;



	// Jump timing
	UPROPERTY(EditDefaultsOnly, Category = "Jump Settings")
	float JumpAnimationDelay;

	UPROPERTY(EditDefaultsOnly, Category = "Jump Settings")
	class UAnimMontage* JumpMontage;

	// Attack cooldown
	UPROPERTY(EditDefaultsOnly, Category = "Combat Settings")
	float AttackCooldown = 1.0f;

	float LastAttackTime = 0.0f;

	FTimerHandle JumpDelayTimer;
	FTimerHandle SprintStaminaDrainTimer;
};

