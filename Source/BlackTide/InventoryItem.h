// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Texture2D.h"
#include "InventoryItem.generated.h"

/**
 * Enum for different item types
 */
UENUM(BlueprintType)
enum class EItemType : uint8
{
	None			UMETA(DisplayName = "None"),
	Consumable		UMETA(DisplayName = "Consumable"),		// Food, drinks, medicine
	Tool			UMETA(DisplayName = "Tool"),			// Axe, pickaxe, hammer
	Weapon			UMETA(DisplayName = "Weapon"),			// Sword, gun, bow
	Armor			UMETA(DisplayName = "Armor"),			// Helmet, chest, boots
	Resource		UMETA(DisplayName = "Resource"),		// Wood, stone, metal
	Building		UMETA(DisplayName = "Building"),		// Walls, foundations, doors
	Misc			UMETA(DisplayName = "Miscellaneous")	// Other items
};



/**
 * Structure defining consumable item effects
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FConsumableEffect
{
	GENERATED_BODY()

	// Health/Hunger/Thirst restoration values
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Consumable")
	float HealthRestore = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Consumable")
	float HungerRestore = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Consumable")
	float ThirstRestore = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Consumable")
	float StaminaRestore = 0.0f;

	// Consumption time (how long it takes to eat/drink)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Consumable")
	float ConsumptionTime = 2.0f;

	FConsumableEffect()
	{
		HealthRestore = 0.0f;
		HungerRestore = 0.0f;
		ThirstRestore = 0.0f;
		StaminaRestore = 0.0f;
		ConsumptionTime = 2.0f;
	}
};

/**
 * Core inventory item structure
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FInventoryItem
{
	GENERATED_BODY()

	// Basic item information
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FString ItemID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FString Name;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FString Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	EItemType ItemType;

	// Visual representation
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	TSoftObjectPtr<UTexture2D> Icon;

	// Note: All items are single-slot (1x1) in BlackTide

	// Stacking
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	int32 MaxStackSize = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	int32 CurrentStackSize = 1;

	// Value and weight
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	int32 Value = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float Weight = 0.0f;

	// Durability (for tools/weapons)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float MaxDurability = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float CurrentDurability = 100.0f;

	// Consumable effects (only used if ItemType == Consumable)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FConsumableEffect ConsumableEffect;

	// Unique identifier for this specific item instance
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FGuid UniqueID = FGuid::NewGuid();

	// Constructor
	FInventoryItem()
	{
		ItemID = TEXT("");
		Name = TEXT("Unknown Item");
		Description = TEXT("No description available");
		ItemType = EItemType::None;
		Icon = nullptr;
		MaxStackSize = 1;
		CurrentStackSize = 1;
		Value = 0;
		Weight = 0.0f;
		MaxDurability = 100.0f;
		CurrentDurability = 100.0f;
		UniqueID = FGuid::NewGuid();
	}

	// Helper functions
	bool IsValid() const
	{
		return !ItemID.IsEmpty() && !Name.IsEmpty();
	}

	bool IsStackable() const
	{
		return MaxStackSize > 1;
	}

	bool CanStackWith(const FInventoryItem& Other) const
	{
		return ItemID == Other.ItemID && 
			   IsStackable() && 
			   CurrentStackSize < MaxStackSize &&
			   Other.CurrentStackSize < Other.MaxStackSize;
	}

	bool IsConsumable() const
	{
		return ItemType == EItemType::Consumable;
	}

	bool IsDamaged() const
	{
		return CurrentDurability < MaxDurability;
	}

	float GetDurabilityPercent() const
	{
		return MaxDurability > 0.0f ? (CurrentDurability / MaxDurability) : 1.0f;
	}

	// All items occupy exactly 1 slot in BlackTide
	int32 GetGridSize() const
	{
		return 1;
	}

	// Equality operator for comparing items
	bool operator==(const FInventoryItem& Other) const
	{
		return UniqueID == Other.UniqueID;
	}

	bool operator!=(const FInventoryItem& Other) const
	{
		return !(*this == Other);
	}
};
