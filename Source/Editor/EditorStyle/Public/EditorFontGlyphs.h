// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

class FText;

class EDITORSTYLE_API FEditorFontGlyphs
{
public:
	// (laquo, quote, previous, back) http://fortawesome.github.io/Font-Awesome/icon/angle-double-left/
	static FText Angle_Double_Left;

	// (raquo, quote, next, forward) http://fortawesome.github.io/Font-Awesome/icon/angle-double-right/
	static FText Angle_Double_Right;

	// (previous, back) http://fortawesome.github.io/Font-Awesome/icon/angle-left/
	static FText Angle_Left;

	// (next, forward) http://fortawesome.github.io/Font-Awesome/icon/angle-right/
	static FText Angle_Right;

	// http://fortawesome.github.io/Font-Awesome/icon/angle-up/
	static FText Angle_Up;

	// (box, storage) http://fortawesome.github.io/Font-Awesome/icon/archive/
	static FText Archive;

	// (next, forward) http://fortawesome.github.io/Font-Awesome/icon/arrow-circle-o-right/
	static FText Arrow_Circle_O_Right;

	// (download) http://fortawesome.github.io/Font-Awesome/icon/arrow-down/
	static FText Arrow_Down;

	// (previous, back) http://fortawesome.github.io/Font-Awesome/icon/arrow-left/
	static FText Arrow_Left;

	// (next, forward) http://fortawesome.github.io/Font-Awesome/icon/arrow-right/
	static FText Arrow_Right;

	// (move, reorder, resize) http://fortawesome.github.io/Font-Awesome/icon/arrows/
	static FText Arrows;

	// (delete, remove, trash, hide, block, stop, abort, cancel) http://fortawesome.github.io/Font-Awesome/icon/ban/
	static FText Ban;

	// (menu, drag, reorder, settings, list, ul, ol, checklist, todo, list, hamburger) http://fortawesome.github.io/Font-Awesome/icon/bars/
	static FText Bars;

	// (read, documentation) http://fortawesome.github.io/Font-Awesome/icon/book/
	static FText Book;

	// (report, insect) http://fortawesome.github.io/Font-Awesome/icon/bug/
	static FText Bug;

	// (vehicle) http://fortawesome.github.io/Font-Awesome/icon/car/
	static FText Car;

	// (more, dropdown, menu, triangle down) http://fortawesome.github.io/Font-Awesome/icon/caret-down/
	static FText Caret_Down;

	// (next, forward, triangle right) http://fortawesome.github.io/Font-Awesome/icon/caret-right/
	static FText Caret_Right;

	// (more, dropdown, menu) http://fortawesome.github.io/Font-Awesome/icon/caret-square-o-down/
	static FText Caret_Square_O_Down;

	// (previous, back) http://fortawesome.github.io/Font-Awesome/icon/caret-square-o-left/
	static FText Caret_Square_O_Left;

	// (next, forward) http://fortawesome.github.io/Font-Awesome/icon/caret-square-o-right/
	static FText Caret_Square_O_Right;

	// http://fortawesome.github.io/Font-Awesome/icon/caret-square-o-up/
	static FText Caret_Square_O_Up;

	// (triangle up) http://fortawesome.github.io/Font-Awesome/icon/caret-up/
	static FText Caret_Up;

	// (checkmark, done, todo, agree, accept, confirm, tick) http://fortawesome.github.io/Font-Awesome/icon/check/
	static FText Check;

	// (todo, done, agree, accept, confirm) http://fortawesome.github.io/Font-Awesome/icon/check-circle/
	static FText Check_Circle;

	// (dot, notification) http://fortawesome.github.io/Font-Awesome/icon/circle/
	static FText Circle;

	// http://fortawesome.github.io/Font-Awesome/icon/circle-o/
	static FText Circle_O;

	// (watch, timer, late, timestamp) http://fortawesome.github.io/Font-Awesome/icon/clock-o/
	static FText Clock_O;

	// (settings) http://fortawesome.github.io/Font-Awesome/icon/cog/
	static FText Cog;

	// (settings) http://fortawesome.github.io/Font-Awesome/icon/cogs/
	static FText Cogs;

	// (picker) http://fortawesome.github.io/Font-Awesome/icon/crosshairs/
	static FText Crosshairs;

	// http://fortawesome.github.io/Font-Awesome/icon/database/
	static FText Database;

	// (import) http://fortawesome.github.io/Font-Awesome/icon/download/
	static FText Download;

	// http://fortawesome.github.io/Font-Awesome/icon/eraser/
	static FText Eraser;

	// (transfer, arrows) http://fortawesome.github.io/Font-Awesome/icon/exchange/
	static FText Exchange;

	// (warning, error, problem, notification, notify, alert) http://fortawesome.github.io/Font-Awesome/icon/exclamation/
	static FText Exclamation;

	// (warning, error, problem, notification, alert) http://fortawesome.github.io/Font-Awesome/icon/exclamation-triangle/
	static FText Exclamation_Triangle;

	// (enlarge, bigger, resize) http://fortawesome.github.io/Font-Awesome/icon/expand/
	static FText Expand;

	// (open, new) http://fortawesome.github.io/Font-Awesome/icon/external-link/
	static FText External_Link;

	// (show, visible, views) http://fortawesome.github.io/Font-Awesome/icon/eye/
	static FText Eye;

	// (toggle, show, hide, visible, visiblity, views) http://fortawesome.github.io/Font-Awesome/icon/eye-slash/
	static FText Eye_Slash;

	// (next, end, last) http://fortawesome.github.io/Font-Awesome/icon/fast-forward/
	static FText Fast_Forward;

	// (new, page, pdf, document) http://fortawesome.github.io/Font-Awesome/icon/file/
	static FText File;

	// (movie) http://fortawesome.github.io/Font-Awesome/icon/film/
	static FText Film;

	// (funnel, options) http://fortawesome.github.io/Font-Awesome/icon/filter/
	static FText Filter;

	// http://fortawesome.github.io/Font-Awesome/icon/floppy-o/
	static FText Floppy_O;

	// http://fortawesome.github.io/Font-Awesome/icon/folder/
	static FText Folder;

	// http://fortawesome.github.io/Font-Awesome/icon/folder-open/
	static FText Folder_Open;

	// http://fortawesome.github.io/Font-Awesome/icon/hourglass/
	static FText Hourglass;

	// http://fortawesome.github.io/Font-Awesome/icon/hourglass-o/
	static FText Hourglass_O;

	// (help, information, more, details) http://fortawesome.github.io/Font-Awesome/icon/info/
	static FText Info;

	// (help, information, more, details) http://fortawesome.github.io/Font-Awesome/icon/info-circle/
	static FText Info_Circle;

	// http://fortawesome.github.io/Font-Awesome/icon/level-down/
	static FText Level_Down;

	// http://fortawesome.github.io/Font-Awesome/icon/level-up/
	static FText Level_Up;

	// (idea, inspiration) http://fortawesome.github.io/Font-Awesome/icon/lightbulb-o/
	static FText Lightbulb_O;

	// (graph, analytics) http://fortawesome.github.io/Font-Awesome/icon/line-chart/
	static FText Line_Chart;

	// (chain) http://fortawesome.github.io/Font-Awesome/icon/link/
	static FText Link;

	// (protect, admin) http://fortawesome.github.io/Font-Awesome/icon/lock/
	static FText Lock;

	// http://fortawesome.github.io/Font-Awesome/icon/long-arrow-down/
	static FText Long_Arrow_Down;

	// http://fortawesome.github.io/Font-Awesome/icon/long-arrow-right/
	static FText Long_Arrow_Right;

	// http://fortawesome.github.io/Font-Awesome/icon/map/
	static FText Map;

	// (map, pin, location, coordinates, localize, address, travel, where, place) http://fortawesome.github.io/Font-Awesome/icon/map-marker/
	static FText Map_Marker;

	// (hide, minify, delete, remove, trash, hide, collapse) http://fortawesome.github.io/Font-Awesome/icon/minus/
	static FText Minus;

	// (delete, remove, trash, hide) http://fortawesome.github.io/Font-Awesome/icon/minus-circle/
	static FText Minus_Circle;

	// http://fortawesome.github.io/Font-Awesome/icon/paper-plane/
	static FText Paper_Plane;

	// (wait) http://fortawesome.github.io/Font-Awesome/icon/pause/
	static FText Pause;

	// http://fortawesome.github.io/Font-Awesome/icon/pause-circle/
	static FText Pause_Circle;

	// (write, edit, update) http://fortawesome.github.io/Font-Awesome/icon/pencil/
	static FText Pencil;

	// (write, edit, update) http://fortawesome.github.io/Font-Awesome/icon/pencil-square/
	static FText Pencil_Square;

	// (start, playing, music, sound) http://fortawesome.github.io/Font-Awesome/icon/play/
	static FText Play;

	// (start, playing) http://fortawesome.github.io/Font-Awesome/icon/play-circle/
	static FText Play_Circle;

	// http://fortawesome.github.io/Font-Awesome/icon/plug/
	static FText Plug;

	// (add, new, create, expand) http://fortawesome.github.io/Font-Awesome/icon/plus/
	static FText Plus;

	// (add, new, create, expand) http://fortawesome.github.io/Font-Awesome/icon/plus-circle/
	static FText Plus_Circle;

	// (help, information, unknown, support) http://fortawesome.github.io/Font-Awesome/icon/question/
	static FText Question;

	// (sort, shuffle) http://fortawesome.github.io/Font-Awesome/icon/random/
	static FText Random;

	// http://fortawesome.github.io/Font-Awesome/icon/recycle/
	static FText Recycle;

	// (reload, sync) http://fortawesome.github.io/Font-Awesome/icon/refresh/
	static FText Refresh;

	// http://fortawesome.github.io/Font-Awesome/icon/share/
	static FText Share;

	// http://fortawesome.github.io/Font-Awesome/icon/share-alt/
	static FText Share_Alt;

	// (enter, join, log in, login, sign up, sign in, signin, signup, arrow) http://fortawesome.github.io/Font-Awesome/icon/sign-in/
	static FText Sign_In;

	// (log out, logout, leave, exit, arrow) http://fortawesome.github.io/Font-Awesome/icon/sign-out/
	static FText Sign_Out;

	// (block, box) http://fortawesome.github.io/Font-Awesome/icon/square/
	static FText Square;

	// (award, achievement, night, rating, score) http://fortawesome.github.io/Font-Awesome/icon/star/
	static FText Star;

	// (next, end, last) http://fortawesome.github.io/Font-Awesome/icon/step-forward/
	static FText Step_Forward;

	// (block, box, square) http://fortawesome.github.io/Font-Awesome/icon/stop/
	static FText Stop;

	// (command, prompt, code) http://fortawesome.github.io/Font-Awesome/icon/terminal/
	static FText Terminal;

	// (blocks, squares, boxes) http://fortawesome.github.io/Font-Awesome/icon/th/
	static FText Th;

	// (marker, pin, location, coordinates) http://fortawesome.github.io/Font-Awesome/icon/thumb-tack/
	static FText Thumb_Tack;

	// (close, exit, x, cross) http://fortawesome.github.io/Font-Awesome/icon/times/
	static FText Times;

	// (close, exit, x) http://fortawesome.github.io/Font-Awesome/icon/times-circle/
	static FText Times_Circle;

	// (garbage, delete, remove, hide) http://fortawesome.github.io/Font-Awesome/icon/trash/
	static FText Trash;

	// (back) http://fortawesome.github.io/Font-Awesome/icon/undo/
	static FText Undo;

	// (protect, admin, password, lock) http://fortawesome.github.io/Font-Awesome/icon/unlock/
	static FText Unlock;

	// (film, movie, record) http://fortawesome.github.io/Font-Awesome/icon/video-camera/
	static FText Video_Camera;
};
