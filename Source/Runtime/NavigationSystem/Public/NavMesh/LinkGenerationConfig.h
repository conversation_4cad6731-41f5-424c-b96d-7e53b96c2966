// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once

#include "Templates/SubclassOf.h"
#include "AI/Navigation/NavigationTypes.h"

#include "LinkGenerationConfig.generated.h"

class UBaseGeneratedNavLinksProxy;
class UNavAreaBase;

#if WITH_RECAST
struct dtNavLinkBuilderJumpDownConfig;
struct dtNavLinkBuilderJumpOverConfig;
#endif //WITH_RECAST

UENUM(meta = (Bitflags, UseEnumValuesAsMaskValuesInEditor = "true"))
enum class ENavLinkBuilderFlags : uint16
{
	CreateCenterPointLink		= 1 << 0,
	CreateExtremityLink			= 1 << 1,
};
ENUM_CLASS_FLAGS(ENavLinkBuilderFlags);

/** Experimental configuration to generate vertical links. */
USTRUCT()
struct FNavLinkGenerationJumpDownConfig
{
	GENERATED_BODY()

	FNavLinkGenerationJumpDownConfig();

	/** Should this config be used to generate links. */
	UPROPERTY(Config)		// @todo Hidden for now. Since there is currently only one config, it has no real usage.
	bool bEnabled = true;
	
	/** Horizontal length of the jump.
	 * How far from the starting point we will look for ground. */ 
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=0, ClampMin=0))
	float JumpLength = 150.f; 

	/** How far from the navmesh edge is the jump started. */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=0, ClampMin=0))
	float JumpDistanceFromEdge = 10.f; 

	/** How far below the starting height we want to look for landing ground. */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=0, ClampMin=0))
	float JumpMaxDepth = 150.f;

	/** Peak height relative to the height of the starting point. */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=0, ClampMin=0))
	float JumpHeight = 50.f;

	/** Tolerance at both ends of the jump to find ground. */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=0, ClampMin=0))
	float JumpEndsHeightTolerance = 80.f;

	/** Value multiplied by CellSize to find the distance between sampling trajectories. Default is 1.
     *  Larger values improve generation speed but might introduce sampling errors.  */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=1, ClampMin=1))
	float SamplingSeparationFactor = 1.f;
	
	/** When filtering similar links, it's the distance used to compare between segment endpoints to match similar links.
	 * Use greater distance for more filtering (0 to deactivate filtering). */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta=(UIMin=0, ClampMin=0))
	float FilterDistanceThreshold = 80.f;

	/** Flags used to indicate how links will be added. */
	UPROPERTY(EditAnywhere, Config, Category = Settings, meta = (Bitmask, BitmaskEnum = "/Script/NavigationSystem.ENavLinkBuilderFlags"))
	uint16 LinkBuilderFlags = (uint16)ENavLinkBuilderFlags::CreateCenterPointLink;

	/** Area class for links generated by this configuration. */
	UPROPERTY(EditAnywhere, Config, Category = Settings)
	TSubclassOf<UNavAreaBase> AreaClass;

	/** Class used to handle links made with this configuration.
	 * Using this allows to implement custom behaviors when using navlinks, for example during the pathfollow.
	 * Note that having a proxy is not required for successful navlink pathfinding,
	 * but it does allow for custom behavior at the start and the end of a given navlink.
	 * This implies that using LinkProxyClass is optional, and it can remain empty (the default value).
	 * @see INavLinkCustomInterface 
	 * @see UGeneratedNavLinksProxy
	 */
	UPROPERTY(EditAnywhere, Category= Settings)
	TSubclassOf<UBaseGeneratedNavLinksProxy> LinkProxyClass;

	/** Identifier used identify the current proxy handler. All links generated through this config will use the same handler. */
	UPROPERTY()
	FNavLinkId LinkProxyId;

	/** Current proxy. The proxy instance is build from the LinkProxyClass (provided it's not null).
	 * A proxy will be created if a @see LinkProxyClass is used.
	 */
	UPROPERTY(Transient)
	TObjectPtr<UBaseGeneratedNavLinksProxy> LinkProxy = nullptr;

	/** Is the link proxy registered to the navigation system CustomNavLinksMap.
	 * Registration occurs on PostRegisterAllComponents or on PostLoadPreRebuild if a new proxy was created. */
	UPROPERTY(Transient)
	bool bLinkProxyRegistered = false;

#if WITH_RECAST	
	/** Copy configuration to dtNavLinkBuilderJumpDownConfig. */
	void CopyToDetourConfig(dtNavLinkBuilderJumpDownConfig& OutDetourConfig) const;
#endif //WITH_RECAST
};

