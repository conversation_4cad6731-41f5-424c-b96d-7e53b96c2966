<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>CityHash</Name>
  <Location>/Engine/Source/Runtime/Core/</Location>
  <Function>Is a high quality and fast Hashfunction that performs reasonably well across all platforms. Used as input for hash-tables to improve performance by decreasing collision rate.</Function>
  <Eula>https://github.com/google/cityhash/blob/master/COPYING</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
	<EndUserGroup>Github</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/CityHash_License.txt</LicenseFolder>
</TpsData>