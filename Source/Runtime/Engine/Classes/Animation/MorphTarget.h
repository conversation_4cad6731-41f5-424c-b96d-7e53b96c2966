// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "UObject/Object.h"
#include "PackedNormal.h"
#include "UObject/EditorObjectVersion.h"
#include "UObject/FortniteMainBranchObjectVersion.h"
#include "UObject/UE5PrivateFrostyStreamObjectVersion.h"
#include "Serialization/MemoryArchive.h"

#include "MorphTarget.generated.h"

class USkeletalMesh;
class UStaticMesh;

/** Morph mesh vertex data used for rendering */
struct FMorphTargetDelta
{
	/** change in position */
	FVector3f			PositionDelta;

	/** Tangent basis normal */
	FVector3f			TangentZDelta;

	/** index of source vertex to apply deltas to */
	uint32			SourceIdx;

	/** pipe operator */
	friend FArchive& operator<<(FArchive& Ar, FMorphTargetDelta& V)
	{
		if ((Ar.UE<PERSON>er() < VER_UE4_MORPHTARGET_CPU_TANGENTZDELTA_FORMATCHANGE) && Ar.IsLoading())
		{
			/** old format of change in tangent basis normal */
			FDeprecatedSerializedPackedNormal TangentZDelta_DEPRECATED;
			Ar << V.PositionDelta << TangentZDelta_DEPRECATED << V.SourceIdx;
			V.TangentZDelta = TangentZDelta_DEPRECATED;
		}
		else
		{
			Ar << V.PositionDelta << V.TangentZDelta << V.SourceIdx;
		}
		return Ar;
	}
};


/**
* Mesh data for a single LOD model of a morph target
*/
struct FMorphTargetLODModel
{
	/** vertex data for a single LOD morph mesh */
	TArray<FMorphTargetDelta> Vertices;

	/** Number of elements in Vertices array. This property is set at runtime and is not serialized. */
	int32 NumVertices;

	/** number of original verts in the base mesh */
	int32 NumBaseMeshVerts;
	
	/** list of sections this morph is used */
	TArray<int32> SectionIndices;

	/** Is this LOD generated by reduction setting */
	bool bGeneratedByEngine;

	/* The source filename use to import this morph target. If source is empty this morph target was import with the LOD geometry. */
	FString SourceFilename;

	FMorphTargetLODModel()
		: NumVertices(0)
		, NumBaseMeshVerts(0)
		, bGeneratedByEngine(false)
	{ }

	/** pipe operator */
	friend FArchive& operator<<(FArchive& Ar, FMorphTargetLODModel& M);

	void Reset()
	{
		Vertices.Reset();
		NumVertices = 0;
		NumBaseMeshVerts = 0;
		SectionIndices.Reset();
		// since engine cleared it, we mark as engine generated
		// this makes it clear to clear up later
		bGeneratedByEngine = true;

		SourceFilename.Empty();
	}

	void DiscardVertexData()
	{
		// Vertices array can be discarded after render data is initialized and if CPU data is no longer required,
		// but cache the array size so that UMorphTarget::HasValidData() can still query if morph target is valid. 
		if (Vertices.Num())
		{
			NumVertices = Vertices.Num();
			Vertices.Empty();
		}
	}
};

#if WITH_EDITOR
/**
* Data to cache serialization results for async asset building
*/
struct FFinishBuildMorphTargetData
{
public:
	virtual ~FFinishBuildMorphTargetData()
	{}
	
	/** Load morph target data */
	ENGINE_API virtual void LoadFromMemoryArchive(FMemoryArchive & Ar);
	
	/** Apply serialized data to skeletal mesh in game thread */
	ENGINE_API virtual void ApplyEditorData(USkeletalMesh * SkeletalMesh, bool bIsSerializeSaving) const;
	
protected:
	bool bApplyMorphTargetsData = false;
	TMap<FName, TArray<FMorphTargetLODModel>> MorphLODModelsPerTargetName;
	};
#endif

UCLASS(hidecategories=Object, MinimalAPI)
class UMorphTarget
	: public UObject
{
	GENERATED_UCLASS_BODY()

public:

	/** USkeletalMesh that this vertex animation works on. */
	UPROPERTY(AssetRegistrySearchable)
	TObjectPtr<class USkeletalMesh> BaseSkelMesh;

	/** morph mesh vertex data for each LOD */
	virtual const TArray<FMorphTargetLODModel>& GetMorphLODModels() const { return MorphLODModels; }

	/** morph mesh vertex data for each LOD */
	virtual TArray<FMorphTargetLODModel>& GetMorphLODModels() { return MorphLODModels; }

protected:
	/** morph mesh vertex data for each LOD */
	TArray<FMorphTargetLODModel>	MorphLODModels;

public:

	/** Get Morphtarget Delta array for the given input Index */
	ENGINE_API virtual const FMorphTargetDelta* GetMorphTargetDelta(int32 LODIndex, int32& OutNumDeltas) const;
	ENGINE_API virtual bool HasDataForLOD(int32 LODIndex) const;
	/** return true if this morphtarget contains data for section within LOD */
	ENGINE_API virtual bool HasDataForSection(int32 LODIndex, int32 SectionIndex) const;
	/** return true if this morphtarget contains valid vertices */
	ENGINE_API virtual bool HasValidData() const;
	ENGINE_API virtual void EmptyMorphLODModels();

	/** return true if this morphtarget was custom imported from a file */
	ENGINE_API virtual bool IsCustomImported(int32 LODIndex) const;
	ENGINE_API virtual FString GetCustomImportedSourceFilename(int32 LODIndex) const;
	ENGINE_API virtual void SetCustomImportedSourceFilename(int32 LODIndex, const FString& InSourceFilename);

	/** Discard CPU Buffers after render resources have been created. */
	UE_DEPRECATED(5.0, "No longer in use, will be deleted. Whether to discard vertex data is now determined during cooking instead of loading.")
	ENGINE_API virtual void DiscardVertexData();

	/** Return true if this morph target uses engine built-in compression */
	virtual bool UsesBuiltinMorphTargetCompression() const { return true; }

#if WITH_EDITOR
	/** Populates the given morph target LOD model with the provided deltas */
	ENGINE_API virtual void PopulateDeltas(const TArray<FMorphTargetDelta>& Deltas, const int32 LODIndex, const TArray<struct FSkelMeshSection>& Sections, const bool bCompareNormal = false, const bool bGeneratedByReductionSetting = false, const float PositionThreshold = UE_THRESH_POINTS_ARE_NEAR);
	/** Remove empty LODModels */
	ENGINE_API virtual void RemoveEmptyMorphTargets();
	/** Factory function to define type of FinishBuildData needed*/
	ENGINE_API virtual TUniquePtr<FFinishBuildMorphTargetData> CreateFinishBuildMorphTargetData() const;
#endif // WITH_EDITOR

public:

	//~ UObject interface

	ENGINE_API virtual void Serialize(FArchive& Ar) override;
#if WITH_EDITORONLY_DATA
	ENGINE_API static void DeclareCustomVersions(FArchive& Ar, const UClass* SpecificSubclass);
#endif
	ENGINE_API virtual void PostLoad() override;

	/** UObject does not support serialization via FMemoryArchive, so manually handle separately */
	ENGINE_API virtual void SerializeMemoryArchive(FMemoryArchive& Ar);
};
